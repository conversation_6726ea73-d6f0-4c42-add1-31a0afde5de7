#!/bin/bash

# LeaderBox Deployment Script for Dokploy
# This script helps prepare and deploy the application

set -e  # Exit on any error

echo "🚀 LeaderBox Deployment Script"
echo "================================"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    echo "❌ Error: pnpm is not installed. Please install pnpm first:"
    echo "   npm install -g pnpm"
    exit 1
fi

# Function to check if .env file exists
check_env_file() {
    if [ ! -f ".env" ]; then
        echo "⚠️  Warning: .env file not found."
        echo "   Copying .env.example to .env..."
        cp .env.example .env
        echo "   ✅ Please edit .env file with your actual values before deploying."
        return 1
    fi
    return 0
}

# Function to install dependencies
install_dependencies() {
    echo "📦 Installing dependencies..."
    pnpm install --frozen-lockfile
    echo "   ✅ Dependencies installed successfully"
}

# Function to run type checking
type_check() {
    echo "🔍 Running type checks..."
    pnpm run type-check
    echo "   ✅ Type checking passed"
}

# Function to run linting
lint_check() {
    echo "🧹 Running linting..."
    pnpm run lint
    echo "   ✅ Linting passed"
}

# Function to build the application
build_app() {
    echo "🏗️  Building application..."
    pnpm run build
    echo "   ✅ Build completed successfully"
}

# Function to test Docker build
test_docker_build() {
    echo "🐳 Testing Docker build..."
    docker build -t leaderbox-test .
    echo "   ✅ Docker build successful"
    
    # Clean up test image
    docker rmi leaderbox-test
    echo "   🧹 Cleaned up test image"
}

# Function to generate Prisma client
generate_prisma() {
    echo "🗄️  Generating Prisma client..."
    pnpm run db:generate
    echo "   ✅ Prisma client generated"
}

# Main deployment preparation
main() {
    echo "Starting deployment preparation..."
    echo ""
    
    # Check environment file
    if ! check_env_file; then
        echo ""
        echo "❌ Please configure your .env file before continuing."
        echo "   Edit the .env file with your database URL, secrets, and other configuration."
        echo "   Then run this script again."
        exit 1
    fi
    
    # Install dependencies
    install_dependencies
    echo ""
    
    # Generate Prisma client
    generate_prisma
    echo ""
    
    # Run checks
    type_check
    echo ""
    
    lint_check
    echo ""
    
    # Build application
    build_app
    echo ""
    
    # Test Docker build (optional)
    read -p "🐳 Do you want to test the Docker build? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        test_docker_build
        echo ""
    fi
    
    echo "✅ Deployment preparation completed successfully!"
    echo ""
    echo "📋 Next steps for Dokploy deployment:"
    echo "   1. Push your code to Git repository"
    echo "   2. Create new application in Dokploy dashboard"
    echo "   3. Configure environment variables in Dokploy"
    echo "   4. Set build command: pnpm run build"
    echo "   5. Set start command: pnpm start"
    echo "   6. Deploy!"
    echo ""
    echo "📖 For detailed instructions, see DEPLOYMENT.md"
}

# Handle script arguments
case "${1:-}" in
    "install")
        install_dependencies
        ;;
    "build")
        build_app
        ;;
    "check")
        type_check
        lint_check
        ;;
    "docker")
        test_docker_build
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  install    Install dependencies only"
        echo "  build      Build application only"
        echo "  check      Run type checking and linting only"
        echo "  docker     Test Docker build only"
        echo "  help       Show this help message"
        echo ""
        echo "Run without arguments to perform full deployment preparation."
        ;;
    "")
        main
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo "Run '$0 help' for usage information."
        exit 1
        ;;
esac
