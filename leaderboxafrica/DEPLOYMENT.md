# LeaderBox Deployment Guide

This guide covers deploying LeaderBox using Dokploy, a modern deployment platform.

## Prerequisites

- Dokploy server set up and running
- Domain name configured
- PostgreSQL database (can be provided by Dokploy)
- Git repository access

## Quick Deployment with Dokploy

### 1. Prepare Your Environment

1. **Clone the repository** to your Dokploy server or ensure Git access
2. **Copy environment file**:
   ```bash
   cp .env.example .env
   ```
3. **Configure environment variables** (see Environment Configuration below)

### Important Notes for Deployment

- **Sensitive Data**: Never include sensitive environment variables (API keys, secrets) in Dockerfile or commit them to Git
- **PostCSS Configuration**: The app uses Tailwind CSS v3 with standard PostCSS plugin
- **Package Manager**: This project uses pnpm, not npm
- **Node Version**: Requires Node.js 20 or higher

### 2. Dokploy Configuration

1. **Create New Application** in Dokploy dashboard
2. **Set Application Details**:
   - Name: `leaderbox`
   - Repository: Your Git repository URL
   - Branch: `main` (or your deployment branch)
   - Build Command: `pnpm run build`
   - Start Command: `pnpm start`
   - Port: `3000`

3. **Configure Build Settings**:
   - Node.js Version: `20`
   - Package Manager: `pnpm`
   - Build Directory: `leaderbox-sveltekit`
   - Install Command: `pnpm install --frozen-lockfile`

### 3. Environment Variables

Configure these essential environment variables in Dokploy:

#### Required Variables
```env
NODE_ENV=production
DATABASE_URL=****************************************/leaderbox
ORIGIN=https://yourdomain.com
PORT=3000
HOST=0.0.0.0
```

#### Email Configuration (Required for auth)
```env
ZEPTOMAIL_API_KEY=your-zeptomail-api-key
ZEPTOMAIL_FROM_EMAIL=<EMAIL>
ZEPTOMAIL_FROM_NAME=LeaderBox
```

#### Security (Recommended)
```env
SESSION_SECRET=your-32-character-or-longer-secret-key
SECURE_COOKIES=true
CORS_ORIGIN=https://yourdomain.com
```

#### Optional Enhancements
```env
REDIS_URL=redis://:password@host:6379
MAX_FILE_SIZE=10485760
ENABLE_REGISTRATION=true
ADMIN_EMAIL=<EMAIL>
```

### 4. Database Setup

#### Option A: Use Dokploy PostgreSQL
1. Create PostgreSQL service in Dokploy
2. Note the connection details
3. Update `DATABASE_URL` in environment variables

#### Option B: External Database
1. Set up PostgreSQL on external provider
2. Create database named `leaderbox`
3. Update `DATABASE_URL` with connection string

#### Database Migration
After deployment, run migrations:
```bash
pnpm run db:migrate:prod
pnpm run db:seed
```

### 5. Domain Configuration

1. **Configure Domain** in Dokploy:
   - Add your domain name
   - Enable SSL/TLS certificate
   - Configure DNS to point to Dokploy server

2. **Update Environment**:
   - Set `ORIGIN` to your domain
   - Set `APP_URL` to your domain
   - Enable `SECURE_COOKIES=true`

## Docker Deployment (Alternative)

If you prefer Docker deployment:

### 1. Build and Run with Docker Compose
```bash
# Copy environment file
cp .env.example .env

# Edit environment variables
nano .env

# Build and start services
docker-compose up -d

# Run database migrations
docker-compose exec app pnpm run db:migrate:prod
docker-compose exec app pnpm run db:seed
```

### 2. Production with Nginx (Optional)
```bash
# Start with nginx proxy
docker-compose --profile production up -d
```

## Post-Deployment Checklist

### 1. Verify Deployment
- [ ] Application loads at your domain
- [ ] Health check endpoint responds: `https://yourdomain.com/health`
- [ ] Database connection working
- [ ] User registration/login working
- [ ] Email sending working (password reset)

### 2. Security Configuration
- [ ] SSL/TLS certificate installed
- [ ] Environment variables secured
- [ ] Admin account created
- [ ] Default passwords changed
- [ ] CORS origins configured

### 3. Performance Optimization
- [ ] Enable gzip compression
- [ ] Configure CDN (optional)
- [ ] Set up monitoring
- [ ] Configure backups

## Monitoring and Maintenance

### Health Checks
The application includes a health check endpoint at `/health` that returns:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-10T12:00:00.000Z",
  "uptime": 3600,
  "environment": "production",
  "version": "1.0.0"
}
```

### Logs
Monitor application logs through Dokploy dashboard or Docker logs:
```bash
# Docker logs
docker-compose logs -f app

# Dokploy logs
# Available in Dokploy dashboard
```

### Database Backups
Set up regular database backups:
```bash
# Manual backup
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# Automated backups (add to cron)
0 2 * * * pg_dump $DATABASE_URL > /backups/leaderbox_$(date +\%Y\%m\%d).sql
```

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version (should be 20+)
   - Verify all dependencies in package.json
   - Check build logs for specific errors
   - Ensure pnpm is being used, not npm

2. **PostCSS/Tailwind Issues**
   - Error: "tailwindcss directly as PostCSS plugin" or "Missing specifier"
   - Solution: Use Tailwind CSS v3 with standard PostCSS plugin
   - Ensure correct import syntax in CSS files

3. **Svelte Accessibility Warnings**
   - Error: "form label must be associated with a control"
   - Solution: Add `id` and `for` attributes to form elements
   - All form labels should have corresponding input IDs

4. **Docker Security Warnings**
   - Error: "Do not use ARG or ENV for sensitive data"
   - Solution: Set sensitive variables through deployment platform
   - Never include API keys or secrets in Dockerfile

5. **Database Connection Issues**
   - Verify DATABASE_URL format
   - Check database server accessibility
   - Ensure database exists and user has permissions

6. **Email Not Working**
   - Verify Zeptomail API key
   - Check email configuration
   - Test with password reset functionality

7. **SSL/HTTPS Issues**
   - Ensure domain DNS points to server
   - Check SSL certificate status
   - Verify ORIGIN environment variable

8. **Nixpacks/Railway Deployment Issues**
   - Use nixpacks.toml for configuration
   - Ensure pnpm is specified as package manager
   - Check Node.js version compatibility

### Getting Help

1. Check Dokploy documentation
2. Review application logs
3. Test health check endpoint
4. Verify environment variables

## Scaling and Performance

### Horizontal Scaling
- Use Dokploy's scaling features
- Configure load balancer
- Set up Redis for session storage

### Database Optimization
- Enable connection pooling
- Set up read replicas
- Configure database indexes

### Caching
- Enable Redis caching
- Configure CDN
- Implement application-level caching

## Security Best Practices

1. **Environment Variables**
   - Never commit secrets to Git
   - Use strong, unique passwords
   - Rotate secrets regularly

2. **Database Security**
   - Use strong database passwords
   - Enable SSL connections
   - Restrict database access

3. **Application Security**
   - Keep dependencies updated
   - Enable HTTPS only
   - Configure proper CORS

4. **Monitoring**
   - Set up error tracking
   - Monitor performance metrics
   - Configure alerts

## Updates and Maintenance

### Deploying Updates
1. Push changes to Git repository
2. Dokploy will automatically rebuild and deploy
3. Monitor deployment logs
4. Verify application functionality

### Database Migrations
```bash
# Run new migrations
npm run db:migrate:prod

# Rollback if needed (be careful!)
npm run db:migrate:rollback
```

### Dependency Updates
```bash
# Update dependencies
npm update

# Security updates
npm audit fix

# Rebuild and deploy
git commit -am "Update dependencies"
git push
```
