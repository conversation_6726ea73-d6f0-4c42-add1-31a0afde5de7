# LeaderBox - SvelteKit Application

A modern political leadership platform built with SvelteKit, Prisma, and PostgreSQL. This is the production-ready version migrated from the React prototype.

## 🚀 Features

- **Leader Profiles**: Detailed profiles of political leaders with ratings and comments
- **Polls & Voting**: Interactive polling system for political issues
- **Social Discussions**: Banter room for political conversations
- **Petitions**: Create and sign petitions for causes
- **Groups**: Political communities and discussions
- **User Management**: Authentication and profile management
- **Admin Dashboard**: Content moderation and system administration

## 🛠 Tech Stack

- **Frontend**: SvelteKit 2.x with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Supabase Auth
- **Styling**: Tailwind CSS
- **Deployment**: Supabase (self-hosted)

## 📋 Prerequisites

- Node.js 18+
- pnpm (preferred package manager)
- PostgreSQL database (Supabase instance)

## 🔧 Development Setup

1. **Install dependencies**:
   ```bash
   pnpm install
   ```

2. **Environment setup**:
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

3. **Database setup**:
   ```bash
   # Generate Prisma client
   pnpm db:generate

   # Push schema to database
   pnpm db:push

   # Seed database with sample data
   pnpm db:seed
   ```

4. **Start development server**:
   ```bash
   pnpm dev
   ```

## 📝 Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm preview` - Preview production build
- `pnpm lint` - Run ESLint
- `pnpm lint:fix` - Fix ESLint issues
- `pnpm format` - Format code with Prettier
- `pnpm format:check` - Check code formatting
- `pnpm db:generate` - Generate Prisma client
- `pnpm db:push` - Push schema to database
- `pnpm db:migrate` - Run database migrations
- `pnpm db:studio` - Open Prisma Studio
- `pnpm db:seed` - Seed database with sample data
- `pnpm type-check` - Run TypeScript type checking

> To deploy your app, you may need to install an [adapter](https://svelte.dev/docs/kit/adapters) for your target environment.
