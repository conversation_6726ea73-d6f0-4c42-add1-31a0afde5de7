// Quick script to check admin user permissions
// Run with: node check-admin-permissions.js

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkAdminPermissions() {
  try {
    console.log('Checking admin user permissions...\n');

    // Find admin users
    const adminUsers = await prisma.user.findMany({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ]
      },
      include: {
        userRole: {
          include: {
            permissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    });

    for (const user of adminUsers) {
      console.log(`👤 User: ${user.email}`);
      console.log(`   Name: ${user.name}`);
      console.log(`   Role ID: ${user.roleId}`);
      console.log(`   Role Name: ${user.userRole?.name || 'No role assigned'}`);
      console.log(`   Email Verified: ${user.emailVerified}`);
      
      if (user.userRole) {
        console.log(`   Permissions (${user.userRole.permissions.length}):`);
        user.userRole.permissions.forEach(rp => {
          console.log(`     - ${rp.permission.name}: ${rp.permission.description}`);
        });
        
        // Check for moderation permissions specifically
        const moderationPermissions = user.userRole.permissions.filter(rp => 
          ['content:moderate', 'leaders:moderate', 'admin:access'].includes(rp.permission.name)
        );
        
        console.log(`   Moderation Permissions: ${moderationPermissions.length > 0 ? '✅' : '❌'}`);
        moderationPermissions.forEach(rp => {
          console.log(`     ✓ ${rp.permission.name}`);
        });
      } else {
        console.log('   ❌ No role assigned to this user!');
      }
      
      console.log('');
    }

    // Check if roles exist
    console.log('📋 Available Roles:');
    const roles = await prisma.role.findMany({
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      }
    });

    roles.forEach(role => {
      console.log(`   ${role.name} (${role.permissions.length} permissions)`);
      if (role.name === 'Admin') {
        const moderationPerms = role.permissions.filter(rp => 
          ['content:moderate', 'leaders:moderate', 'admin:access'].includes(rp.permission.name)
        );
        console.log(`     Moderation permissions: ${moderationPerms.map(rp => rp.permission.name).join(', ')}`);
      }
    });

  } catch (error) {
    console.error('❌ Error checking permissions:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdminPermissions();