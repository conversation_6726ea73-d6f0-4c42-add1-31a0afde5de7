// Quick script to create a Moderator role with moderation permissions
// Run with: node create-moderator-role.js

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createModeratorRole() {
  try {
    console.log('Creating Moderator role...');

    // Create Moderator role
    const moderatorRole = await prisma.role.upsert({
      where: { name: 'Moderator' },
      update: {},
      create: {
        name: 'Moderator',
        description: 'Content moderator with permission to moderate user content',
        isSystem: false,
      },
    });

    console.log('✅ Created Moderator role:', moderatorRole.name);

    // Get required permissions
    const requiredPermissions = [
      'content:moderate',
      'leaders:moderate', 
      'content:read',
      'leaders:read',
      'users:read'
    ];

    const permissions = await prisma.permission.findMany({
      where: {
        name: { in: requiredPermissions }
      }
    });

    console.log('Found permissions:', permissions.map(p => p.name));

    // Assign permissions to Moderator role
    for (const permission of permissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: moderatorRole.id,
            permissionId: permission.id,
          },
        },
        update: {},
        create: {
          roleId: moderatorRole.id,
          permissionId: permission.id,
        },
      });
    }

    console.log('✅ Assigned permissions to Moderator role');

    // List all users so you can choose which one to assign the role to
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        roleId: true
      }
    });

    console.log('\n📋 Available users:');
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email} (${user.name}) - Current role: ${user.roleId || 'None'}`);
    });

    console.log('\n🎉 Moderator role created successfully!');
    console.log('Now you can:');
    console.log('1. Go to /admin/users (as an admin)');
    console.log('2. Assign the "Moderator" role to your user');
    console.log('3. Or update a user directly in the database');

  } catch (error) {
    console.error('❌ Error creating Moderator role:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createModeratorRole();