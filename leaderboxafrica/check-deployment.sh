#!/bin/bash

# Pre-deployment checklist script for LeaderBox
# This script checks for common issues before deployment

set -e

echo "🔍 LeaderBox Pre-Deployment Checklist"
echo "====================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Counters
PASSED=0
FAILED=0
WARNINGS=0

# Function to print status
print_status() {
    local status=$1
    local message=$2
    
    case $status in
        "PASS")
            echo -e "${GREEN}✅ PASS${NC}: $message"
            ((PASSED++))
            ;;
        "FAIL")
            echo -e "${RED}❌ FAIL${NC}: $message"
            ((FAILED++))
            ;;
        "WARN")
            echo -e "${YELLOW}⚠️  WARN${NC}: $message"
            ((WARNINGS++))
            ;;
    esac
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_status "FAIL" "package.json not found. Run from project root."
    exit 1
fi

echo "Checking project structure and configuration..."
echo ""

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -ge 20 ]; then
    print_status "PASS" "Node.js version $NODE_VERSION is supported"
else
    print_status "FAIL" "Node.js version $NODE_VERSION is too old. Requires 20+"
fi

# Check if pnpm is installed
if command -v pnpm &> /dev/null; then
    PNPM_VERSION=$(pnpm --version)
    print_status "PASS" "pnpm version $PNPM_VERSION is installed"
else
    print_status "FAIL" "pnpm is not installed. Run: npm install -g pnpm"
fi

# Check for pnpm-lock.yaml
if [ -f "pnpm-lock.yaml" ]; then
    print_status "PASS" "pnpm-lock.yaml exists"
else
    print_status "FAIL" "pnpm-lock.yaml missing. Run: pnpm install"
fi

# Check environment file
if [ -f ".env" ]; then
    print_status "PASS" ".env file exists"
    
    # Check for required environment variables
    required_vars=("DATABASE_URL" "ORIGIN" "SESSION_SECRET")
    for var in "${required_vars[@]}"; do
        if grep -q "^$var=" .env; then
            print_status "PASS" "$var is set in .env"
        else
            print_status "FAIL" "$var is missing from .env"
        fi
    done
else
    print_status "WARN" ".env file missing. Copy from .env.example"
fi

# Check PostCSS configuration
if [ -f "postcss.config.js" ]; then
    if grep -q "tailwindcss" postcss.config.js; then
        print_status "PASS" "PostCSS configured for Tailwind CSS"
    else
        print_status "FAIL" "PostCSS not configured for Tailwind CSS"
    fi
else
    print_status "FAIL" "postcss.config.js missing"
fi

# Check Tailwind configuration
if [ -f "tailwind.config.js" ]; then
    print_status "PASS" "Tailwind configuration exists"
else
    print_status "FAIL" "tailwind.config.js missing"
fi

# Check SvelteKit configuration
if [ -f "svelte.config.js" ]; then
    if grep -q "@sveltejs/adapter-node" svelte.config.js; then
        print_status "PASS" "SvelteKit configured with Node.js adapter"
    else
        print_status "WARN" "SvelteKit not using Node.js adapter"
    fi
else
    print_status "FAIL" "svelte.config.js missing"
fi

# Check Dockerfile
if [ -f "Dockerfile" ]; then
    print_status "PASS" "Dockerfile exists"
    
    # Check for sensitive data in Dockerfile
    if grep -q "SESSION_SECRET\|API_KEY\|PASSWORD" Dockerfile; then
        print_status "FAIL" "Dockerfile contains sensitive environment variables"
    else
        print_status "PASS" "Dockerfile doesn't contain sensitive data"
    fi
    
    # Check for pnpm usage
    if grep -q "pnpm" Dockerfile; then
        print_status "PASS" "Dockerfile uses pnpm"
    else
        print_status "FAIL" "Dockerfile should use pnpm, not npm"
    fi
else
    print_status "WARN" "Dockerfile missing (optional for some platforms)"
fi

# Check for deployment configurations
if [ -f "nixpacks.toml" ]; then
    print_status "PASS" "Nixpacks configuration exists"
else
    print_status "WARN" "nixpacks.toml missing (needed for Railway/Nixpacks)"
fi

# Check package.json scripts
required_scripts=("build" "start" "db:generate" "db:migrate:prod")
for script in "${required_scripts[@]}"; do
    if grep -q "\"$script\":" package.json; then
        print_status "PASS" "Script '$script' exists in package.json"
    else
        print_status "FAIL" "Script '$script' missing from package.json"
    fi
done

# Check for accessibility issues in Svelte files
echo ""
echo "Checking for common Svelte accessibility issues..."

# Check for form labels without for attributes
if find src -name "*.svelte" -exec grep -l "<label" {} \; | xargs grep -L "for=" > /dev/null 2>&1; then
    print_status "WARN" "Some form labels may be missing 'for' attributes"
else
    print_status "PASS" "Form labels appear to be properly associated"
fi

# Try to run type checking
echo ""
echo "Running type checks..."
if pnpm run type-check > /dev/null 2>&1; then
    print_status "PASS" "TypeScript type checking passed"
else
    print_status "FAIL" "TypeScript type checking failed"
fi

# Try to run linting
echo ""
echo "Running linting..."
if pnpm run lint > /dev/null 2>&1; then
    print_status "PASS" "ESLint passed"
else
    print_status "WARN" "ESLint found issues (run 'pnpm run lint' for details)"
fi

# Summary
echo ""
echo "======================================="
echo "📊 Pre-Deployment Checklist Summary"
echo "======================================="
echo -e "${GREEN}✅ Passed: $PASSED${NC}"
echo -e "${YELLOW}⚠️  Warnings: $WARNINGS${NC}"
echo -e "${RED}❌ Failed: $FAILED${NC}"
echo ""

if [ $FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 Ready for deployment!${NC}"
    if [ $WARNINGS -gt 0 ]; then
        echo -e "${YELLOW}Note: Address warnings for optimal deployment${NC}"
    fi
    exit 0
else
    echo -e "${RED}🚫 Fix failed checks before deploying${NC}"
    exit 1
fi
