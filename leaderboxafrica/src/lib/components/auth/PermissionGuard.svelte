<!--
  PermissionGuard Component
  
  A flexible component for conditional rendering based on user permissions.
  Supports single permission, multiple permissions, and fallback content.
  
  Usage:
  - Single permission: <PermissionGuard permission="users:read">...</PermissionGuard>
  - Multiple permissions (any): <PermissionGuard permissions={["users:read", "users:write"]}>...</PermissionGuard>
  - Multiple permissions (all): <PermissionGuard permissions={["users:read", "users:write"]} requireAll>...</PermissionGuard>
  - With fallback: <PermissionGuard permission="admin:access" fallback>...</PermissionGuard>
  - Resource-based: <PermissionGuard resource="users" action="read" scope="own">...</PermissionGuard>
-->

<script lang="ts">
	import { permissionStore } from '$lib/stores/permissions';
	import type { Snippet } from 'svelte';

	interface Props {
		// Single permission check
		permission?: string;
		
		// Multiple permissions check
		permissions?: string[];
		
		// Whether all permissions are required (default: false - any permission)
		requireAll?: boolean;
		
		// Resource-based permission check
		resource?: string;
		action?: string;
		scope?: string;
		
		// Whether to show fallback content when permission is denied
		fallback?: boolean;
		
		// Loading state behavior
		showLoadingState?: boolean;
		hideWhileLoading?: boolean;
		
		// Content snippets
		children: Snippet;
		fallbackContent?: Snippet;
		loadingContent?: Snippet;
	}

	let {
		permission,
		permissions = [],
		requireAll = false,
		resource,
		action,
		scope,
		fallback = false,
		showLoadingState = false,
		hideWhileLoading = false,
		children,
		fallbackContent,
		loadingContent
	}: Props = $props();

	// Reactive permission check using $derived
	const hasAccess = $derived(() => {
		// Don't show content while loading unless explicitly allowed
		if ($permissionStore.loading && hideWhileLoading) {
			return false;
		}

		// If store is not initialized, deny access by default
		if (!$permissionStore.initialized) {
			return false;
		}

		// Resource-based permission check
		if (resource && action) {
			return permissionStore.canAccess(resource, action, scope);
		}

		// Single permission check
		if (permission) {
			return permissionStore.hasPermission(permission);
		}

		// Multiple permissions check
		if (permissions.length > 0) {
			return requireAll
				? permissionStore.hasAllPermissions(permissions)
				: permissionStore.hasAnyPermission(permissions);
		}

		// If no permission criteria specified, allow access
		return true;
	});

	// Show loading state check
	const shouldShowLoading = $derived(() => {
		return showLoadingState && permissionStore.isLoading && !permissionStore.isInitialized;
	});

	// Debug logging in development
	$effect(() => {
		if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
			const permissionCriteria = permission || permissions.join(', ') || 
				(resource && action ? `${resource}:${action}${scope ? ':' + scope : ''}` : 'none');
			
			console.debug('PermissionGuard:', {
				criteria: permissionCriteria,
				hasAccess: hasAccess,
				isLoading: permissionStore.isLoading,
				isInitialized: permissionStore.isInitialized,
				requireAll,
				fallback
			});
		}
	});
</script>

<!-- Loading state -->
{#if shouldShowLoading}
	{#if loadingContent}
		{@render loadingContent()}
	{:else}
		<div class="flex items-center justify-center p-4">
			<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
			<span class="ml-2 text-sm text-gray-600">Loading permissions...</span>
		</div>
	{/if}
<!-- Permission granted -->
{:else if hasAccess}
	{@render children()}
<!-- Permission denied with fallback -->
{:else if fallback && fallbackContent}
	{@render fallbackContent()}
<!-- Permission denied with default fallback -->
{:else if fallback}
	<div class="text-center p-4 text-gray-500">
		<div class="text-sm">
			{#if permission}
				You don't have permission to access this content.
			{:else if permissions.length > 0}
				You don't have the required permissions to access this content.
			{:else if resource && action}
				You don't have permission to {action} {resource}.
			{:else}
				Access denied.
			{/if}
		</div>
		{#if permissionStore.getError()}
			<div class="text-xs text-red-500 mt-1">
				Error loading permissions: {permissionStore.getError()}
			</div>
		{/if}
	</div>
{/if}