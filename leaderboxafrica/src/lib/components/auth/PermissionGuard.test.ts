import { describe, it, expect, beforeEach, vi, type Mock } from 'vitest';
import { permissionStore } from '$lib/stores/permissions.js';

// Mock the permission store
vi.mock('$lib/stores/permissions.js', () => ({
	permissionStore: {
		isLoading: false,
		isInitialized: true,
		hasPermission: vi.fn(),
		hasAnyPermission: vi.fn(),
		hasAllPermissions: vi.fn(),
		canAccess: vi.fn(),
		getError: vi.fn(() => null)
	}
}));

const mockPermissionStore = permissionStore as {
	isLoading: boolean;
	isInitialized: boolean;
	hasPermission: Mock;
	hasAnyPermission: Mock;
	hasAllPermissions: Mock;
	canAccess: Mock;
	getError: Mock;
};

// Helper function to simulate the permission checking logic from the component
function checkPermissionAccess(props: {
	permission?: string;
	permissions?: string[];
	requireAll?: boolean;
	resource?: string;
	action?: string;
	scope?: string;
	hideWhileLoading?: boolean;
}) {
	const {
		permission,
		permissions = [],
		requireAll = false,
		resource,
		action,
		scope,
		hideWhileLoading = false
	} = props;

	// Don't show content while loading unless explicitly allowed
	if (mockPermissionStore.isLoading && hideWhileLoading) {
		return false;
	}

	// If store is not initialized, deny access by default
	if (!mockPermissionStore.isInitialized) {
		return false;
	}

	// Resource-based permission check
	if (resource && action) {
		return mockPermissionStore.canAccess(resource, action, scope);
	}

	// Single permission check
	if (permission) {
		return mockPermissionStore.hasPermission(permission);
	}

	// Multiple permissions check
	if (permissions.length > 0) {
		return requireAll 
			? mockPermissionStore.hasAllPermissions(permissions)
			: mockPermissionStore.hasAnyPermission(permissions);
	}

	// If no permission criteria specified, allow access
	return true;
}

describe('PermissionGuard Logic', () => {
	beforeEach(() => {
		// Reset all mocks
		vi.clearAllMocks();
		
		// Set default mock values
		mockPermissionStore.isLoading = false;
		mockPermissionStore.isInitialized = true;
		mockPermissionStore.hasPermission.mockReturnValue(false);
		mockPermissionStore.hasAnyPermission.mockReturnValue(false);
		mockPermissionStore.hasAllPermissions.mockReturnValue(false);
		mockPermissionStore.canAccess.mockReturnValue(false);
		mockPermissionStore.getError.mockReturnValue(null);
	});

	describe('Single Permission Check', () => {
		it('should allow access when user has required permission', () => {
			mockPermissionStore.hasPermission.mockReturnValue(true);

			const hasAccess = checkPermissionAccess({
				permission: 'users:read'
			});

			expect(hasAccess).toBe(true);
			expect(mockPermissionStore.hasPermission).toHaveBeenCalledWith('users:read');
		});

		it('should deny access when user lacks required permission', () => {
			mockPermissionStore.hasPermission.mockReturnValue(false);

			const hasAccess = checkPermissionAccess({
				permission: 'users:read'
			});

			expect(hasAccess).toBe(false);
			expect(mockPermissionStore.hasPermission).toHaveBeenCalledWith('users:read');
		});
	});

	describe('Multiple Permissions Check', () => {
		it('should allow access when user has any required permission (default behavior)', () => {
			mockPermissionStore.hasAnyPermission.mockReturnValue(true);

			const hasAccess = checkPermissionAccess({
				permissions: ['users:read', 'users:write']
			});

			expect(hasAccess).toBe(true);
			expect(mockPermissionStore.hasAnyPermission).toHaveBeenCalledWith(['users:read', 'users:write']);
		});

		it('should allow access when user has all required permissions with requireAll=true', () => {
			mockPermissionStore.hasAllPermissions.mockReturnValue(true);

			const hasAccess = checkPermissionAccess({
				permissions: ['users:read', 'users:write'],
				requireAll: true
			});

			expect(hasAccess).toBe(true);
			expect(mockPermissionStore.hasAllPermissions).toHaveBeenCalledWith(['users:read', 'users:write']);
		});

		it('should deny access when user lacks any permission with requireAll=true', () => {
			mockPermissionStore.hasAllPermissions.mockReturnValue(false);

			const hasAccess = checkPermissionAccess({
				permissions: ['users:read', 'users:write'],
				requireAll: true
			});

			expect(hasAccess).toBe(false);
			expect(mockPermissionStore.hasAllPermissions).toHaveBeenCalledWith(['users:read', 'users:write']);
		});

		it('should deny access when user has no permissions', () => {
			mockPermissionStore.hasAnyPermission.mockReturnValue(false);

			const hasAccess = checkPermissionAccess({
				permissions: ['users:read', 'users:write']
			});

			expect(hasAccess).toBe(false);
			expect(mockPermissionStore.hasAnyPermission).toHaveBeenCalledWith(['users:read', 'users:write']);
		});
	});

	describe('Resource-Based Permission Check', () => {
		it('should allow access when user can access resource with action', () => {
			mockPermissionStore.canAccess.mockReturnValue(true);

			const hasAccess = checkPermissionAccess({
				resource: 'users',
				action: 'read'
			});

			expect(hasAccess).toBe(true);
			expect(mockPermissionStore.canAccess).toHaveBeenCalledWith('users', 'read', undefined);
		});

		it('should allow access when user can access resource with action and scope', () => {
			mockPermissionStore.canAccess.mockReturnValue(true);

			const hasAccess = checkPermissionAccess({
				resource: 'users',
				action: 'update',
				scope: 'own'
			});

			expect(hasAccess).toBe(true);
			expect(mockPermissionStore.canAccess).toHaveBeenCalledWith('users', 'update', 'own');
		});

		it('should deny access when user cannot access resource', () => {
			mockPermissionStore.canAccess.mockReturnValue(false);

			const hasAccess = checkPermissionAccess({
				resource: 'users',
				action: 'delete'
			});

			expect(hasAccess).toBe(false);
			expect(mockPermissionStore.canAccess).toHaveBeenCalledWith('users', 'delete', undefined);
		});
	});

	describe('Loading States', () => {
		it('should deny access when store is loading and hideWhileLoading=true', () => {
			mockPermissionStore.isLoading = true;
			mockPermissionStore.hasPermission.mockReturnValue(true);

			const hasAccess = checkPermissionAccess({
				permission: 'users:read',
				hideWhileLoading: true
			});

			expect(hasAccess).toBe(false);
		});

		it('should allow access when store is loading but hideWhileLoading=false', () => {
			mockPermissionStore.isLoading = true;
			mockPermissionStore.hasPermission.mockReturnValue(true);

			const hasAccess = checkPermissionAccess({
				permission: 'users:read',
				hideWhileLoading: false
			});

			expect(hasAccess).toBe(true);
		});
	});

	describe('Store Initialization', () => {
		it('should deny access when store is not initialized', () => {
			mockPermissionStore.isInitialized = false;
			mockPermissionStore.hasPermission.mockReturnValue(true);

			const hasAccess = checkPermissionAccess({
				permission: 'users:read'
			});

			expect(hasAccess).toBe(false);
		});

		it('should allow access when store is initialized and permission granted', () => {
			mockPermissionStore.isInitialized = true;
			mockPermissionStore.hasPermission.mockReturnValue(true);

			const hasAccess = checkPermissionAccess({
				permission: 'users:read'
			});

			expect(hasAccess).toBe(true);
		});
	});

	describe('Error Handling', () => {
		it('should return error state when permission store has error', () => {
			mockPermissionStore.hasPermission.mockReturnValue(false);
			mockPermissionStore.getError.mockReturnValue('Failed to load permissions');

			const hasAccess = checkPermissionAccess({
				permission: 'users:read'
			});

			expect(hasAccess).toBe(false);
			expect(mockPermissionStore.getError()).toBe('Failed to load permissions');
		});
	});

	describe('No Permission Criteria', () => {
		it('should allow access when no permission criteria specified', () => {
			const hasAccess = checkPermissionAccess({});

			expect(hasAccess).toBe(true);
		});
	});

	describe('Priority of Permission Checks', () => {
		it('should prioritize resource-based check over single permission', () => {
			mockPermissionStore.canAccess.mockReturnValue(true);
			mockPermissionStore.hasPermission.mockReturnValue(false);

			const hasAccess = checkPermissionAccess({
				permission: 'users:read',
				resource: 'users',
				action: 'read'
			});

			expect(hasAccess).toBe(true);
			expect(mockPermissionStore.canAccess).toHaveBeenCalledWith('users', 'read', undefined);
			expect(mockPermissionStore.hasPermission).not.toHaveBeenCalled();
		});

		it('should prioritize single permission over multiple permissions', () => {
			mockPermissionStore.hasPermission.mockReturnValue(true);
			mockPermissionStore.hasAnyPermission.mockReturnValue(false);

			const hasAccess = checkPermissionAccess({
				permission: 'users:read',
				permissions: ['users:write']
			});

			expect(hasAccess).toBe(true);
			expect(mockPermissionStore.hasPermission).toHaveBeenCalledWith('users:read');
			expect(mockPermissionStore.hasAnyPermission).not.toHaveBeenCalled();
		});
	});
});