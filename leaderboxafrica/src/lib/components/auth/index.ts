// Permission-aware UI components
// Provides a centralized export for all permission-related components

export { default as PermissionGuard } from './PermissionGuard.svelte';
export { default as PermissionButton } from './PermissionButton.svelte';
export { default as PermissionLink } from './PermissionLink.svelte';
export { default as PermissionMenu } from './PermissionMenu.svelte';
export { default as PermissionCard } from './PermissionCard.svelte';

// Export types for TypeScript support
export type { PermissionMenuItem } from './PermissionMenu.svelte';