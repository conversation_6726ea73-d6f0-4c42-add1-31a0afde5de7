<!--
  PermissionCard Component
  
  A card component that conditionally renders based on user permissions.
  Wraps the base Card component with permission checking capabilities.
  
  Usage:
  - Basic: <PermissionCard permission="users:read">User content</PermissionCard>
  - With fallback: <PermissionCard permission="admin:access" fallback>Admin content</PermissionCard>
  - Resource-based: <PermissionCard resource="leaders" action="read">Leader content</PermissionCard>
-->

<script lang="ts">
	import { permissionStore } from '$lib/stores/permissions';
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import CardTitle from '$lib/components/ui/CardTitle.svelte';
	import type { Snippet } from 'svelte';

	interface Props {
		// Permission checking props
		permission?: string;
		permissions?: string[];
		requireAll?: boolean;
		resource?: string;
		action?: string;
		scope?: string;
		
		// Fallback behavior
		fallback?: boolean;
		fallbackTitle?: string;
		fallbackMessage?: string;
		
		// Card props
		class?: string;
		
		// Content snippets
		children: Snippet;
		header?: Snippet;
		fallbackContent?: Snippet;
	}

	let {
		permission,
		permissions = [],
		requireAll = false,
		resource,
		action,
		scope,
		fallback = false,
		fallbackTitle = 'Access Restricted',
		fallbackMessage = 'You do not have permission to view this content.',
		class: className = '',
		children,
		header,
		fallbackContent
	}: Props = $props();

	// Reactive permission check using $derived
	const hasAccess = $derived(() => {
		// If store is not initialized, deny access by default
		if (!$permissionStore.initialized) {
			return false;
		}

		// Resource-based permission check
		if (resource && action) {
			return permissionStore.canAccess(resource, action, scope);
		}

		// Single permission check
		if (permission) {
			return permissionStore.hasPermission(permission);
		}

		// Multiple permissions check
		if (permissions.length > 0) {
			return requireAll
				? permissionStore.hasAllPermissions(permissions)
				: permissionStore.hasAnyPermission(permissions);
		}

		// If no permission criteria specified, allow access
		return true;
	});

	// Determine what to show
	const shouldShowContent = $derived(() => hasAccess);
	const shouldShowFallback = $derived(() => !hasAccess && fallback);
	const shouldShowCard = $derived(() => shouldShowContent || shouldShowFallback);
</script>

{#if shouldShowCard}
	<Card class={className}>
		{#if header}
			<CardHeader>
				{@render header()}
			</CardHeader>
		{/if}
		
		<CardContent>
			{#if shouldShowContent}
				{@render children()}
			{:else if shouldShowFallback}
				{#if fallbackContent}
					{@render fallbackContent()}
				{:else}
					<div class="text-center py-8">
						<div class="text-gray-400 mb-2">
							<svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
							</svg>
						</div>
						<h3 class="text-lg font-medium text-gray-900 mb-1">{fallbackTitle}</h3>
						<p class="text-sm text-gray-500">{fallbackMessage}</p>
						{#if permissionStore.getError()}
							<p class="text-xs text-red-500 mt-2">
								Error: {permissionStore.getError()}
							</p>
						{/if}
					</div>
				{/if}
			{/if}
		</CardContent>
	</Card>
{/if}