<!--
  PermissionButton Component
  
  A button component that conditionally renders based on user permissions.
  Extends the base Button component with permission checking capabilities.
  
  Usage:
  - Basic: <PermissionButton permission="users:create" onclick={handleClick}>Create User</PermissionButton>
  - Multiple permissions: <PermissionButton permissions={["users:create", "admin:access"]}>Create User</PermissionButton>
  - Resource-based: <PermissionButton resource="users" action="create">Create User</PermissionButton>
  - With fallback: <PermissionButton permission="admin:access" fallback disabled>Admin Only</PermissionButton>
-->

<script lang="ts">
	import { permissionStore } from '$lib/stores/permissions';
	import Button from '$lib/components/ui/Button.svelte';
	import type { Snippet } from 'svelte';

	interface Props {
		// Permission checking props
		permission?: string;
		permissions?: string[];
		requireAll?: boolean;
		resource?: string;
		action?: string;
		scope?: string;
		
		// Fallback behavior
		fallback?: boolean;
		fallbackBehavior?: 'hide' | 'disable' | 'show';
		
		// Button props (pass through to Button component)
		variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
		size?: 'default' | 'sm' | 'lg' | 'icon';
		disabled?: boolean;
		type?: 'button' | 'submit' | 'reset';
		class?: string;
		
		// Event handlers
		onclick?: (event: MouseEvent) => void;
		
		// Content
		children: Snippet;
	}

	let {
		permission,
		permissions = [],
		requireAll = false,
		resource,
		action,
		scope,
		fallback = false,
		fallbackBehavior = 'hide',
		variant = 'default',
		size = 'default',
		disabled = false,
		type = 'button',
		class: className = '',
		onclick,
		children
	}: Props = $props();

	// Reactive permission check using $derived
	const hasAccess = $derived(() => {
		// If store is not initialized, deny access by default
		if (!$permissionStore.initialized) {
			return false;
		}

		// Resource-based permission check
		if (resource && action) {
			return permissionStore.canAccess(resource, action, scope);
		}

		// Single permission check
		if (permission) {
			return permissionStore.hasPermission(permission);
		}

		// Multiple permissions check
		if (permissions.length > 0) {
			return requireAll
				? permissionStore.hasAllPermissions(permissions)
				: permissionStore.hasAnyPermission(permissions);
		}

		// If no permission criteria specified, allow access
		return true;
	});

	// Determine if button should be shown
	const shouldShow = $derived(() => {
		if (hasAccess) return true;
		if (!fallback) return false;
		return fallbackBehavior !== 'hide';
	});

	// Determine if button should be disabled
	const isDisabled = $derived(() => {
		if (disabled) return true;
		if (hasAccess) return false;
		return fallback && fallbackBehavior === 'disable';
	});

	// Handle click with permission check
	function handleClick(event: MouseEvent) {
		// Prevent action if no permission and not in fallback mode
		if (!hasAccess && (!fallback || fallbackBehavior === 'hide')) {
			event.preventDefault();
			event.stopPropagation();
			console.warn('Action prevented: insufficient permissions');
			return;
		}

		// Call the provided onclick handler
		if (onclick) {
			onclick(event);
		}
	}
</script>

{#if shouldShow}
	<Button
		{variant}
		{size}
		{type}
		disabled={isDisabled}
		class={className}
		onclick={handleClick}
	>
		{@render children()}
	</Button>
{/if}