# Permission-Aware UI Components

This directory contains a collection of UI components that integrate with the role-based access control (RBAC) system. These components automatically handle permission checking and conditional rendering based on user permissions.

## Components

### PermissionGuard

A flexible wrapper component for conditional rendering based on permissions.

```svelte
<script>
  import { PermissionGuard } from '$lib/components/auth';
</script>

<!-- Single permission -->
<PermissionGuard permission="users:read">
  <p>This content is only visible to users who can read users</p>
</PermissionGuard>

<!-- Multiple permissions (any) -->
<PermissionGuard permissions={["users:read", "admin:access"]}>
  <p>Visible to users with either permission</p>
</PermissionGuard>

<!-- Multiple permissions (all required) -->
<PermissionGuard permissions={["users:read", "users:write"]} requireAll>
  <p>Visible only to users with both permissions</p>
</PermissionGuard>

<!-- Resource-based permissions -->
<PermissionGuard resource="users" action="create" scope="own">
  <p>Can create own user records</p>
</PermissionGuard>

<!-- With fallback content -->
<PermissionGuard permission="admin:access" fallback>
  <p>Admin content</p>
  {#snippet fallbackContent()}
    <p>You need admin access to view this content</p>
  {/snippet}
</PermissionGuard>

<!-- Loading states -->
<PermissionGuard permission="users:read" showLoadingState hideWhileLoading>
  <p>Protected content</p>
  {#snippet loadingContent()}
    <p>Checking permissions...</p>
  {/snippet}
</PermissionGuard>
```

### PermissionButton

A button component that conditionally renders and handles permissions.

```svelte
<script>
  import { PermissionButton } from '$lib/components/auth';
  
  function handleCreateUser() {
    // Create user logic
  }
</script>

<!-- Basic usage -->
<PermissionButton permission="users:create" onclick={handleCreateUser}>
  Create User
</PermissionButton>

<!-- With fallback behavior -->
<PermissionButton 
  permission="admin:access" 
  fallback 
  fallbackBehavior="disable"
  onclick={handleAdminAction}
>
  Admin Action
</PermissionButton>

<!-- Resource-based -->
<PermissionButton resource="leaders" action="create" variant="outline">
  Add Leader
</PermissionButton>

<!-- Multiple permissions -->
<PermissionButton permissions={["users:create", "admin:access"]} size="sm">
  Quick Create
</PermissionButton>
```

### PermissionLink

A link component that conditionally renders based on permissions.

```svelte
<script>
  import { PermissionLink } from '$lib/components/auth';
</script>

<!-- Basic usage -->
<PermissionLink permission="users:read" href="/users">
  View Users
</PermissionLink>

<!-- With fallback -->
<PermissionLink 
  permission="admin:access" 
  href="/admin" 
  fallback 
  fallbackBehavior="disable"
  class="nav-link"
>
  Admin Panel
</PermissionLink>

<!-- Resource-based -->
<PermissionLink resource="leaders" action="read" href="/leaders">
  Leaders
</PermissionLink>
```

### PermissionMenu

A dropdown menu that filters items based on permissions.

```svelte
<script>
  import { PermissionMenu } from '$lib/components/auth';
  import Button from '$lib/components/ui/Button.svelte';
  
  const menuItems = [
    {
      id: 'view-users',
      label: 'View Users',
      permission: 'users:read',
      onclick: () => goto('/users')
    },
    {
      id: 'create-user',
      label: 'Create User',
      permission: 'users:create',
      onclick: () => goto('/users/create')
    },
    {
      id: 'admin',
      label: 'Administration',
      permission: 'admin:access',
      children: [
        {
          id: 'manage-roles',
          label: 'Manage Roles',
          permission: 'admin:roles',
          onclick: () => goto('/admin/roles')
        },
        {
          id: 'system-settings',
          label: 'System Settings',
          permission: 'admin:settings',
          onclick: () => goto('/admin/settings')
        }
      ]
    }
  ];
</script>

<PermissionMenu items={menuItems}>
  {#snippet trigger()}
    <Button variant="outline">Actions</Button>
  {/snippet}
</PermissionMenu>
```

### PermissionCard

A card component that conditionally shows content based on permissions.

```svelte
<script>
  import { PermissionCard } from '$lib/components/auth';
</script>

<!-- Basic usage -->
<PermissionCard permission="users:read">
  <h3>User Statistics</h3>
  <p>Total users: 1,234</p>
</PermissionCard>

<!-- With custom fallback -->
<PermissionCard permission="admin:access" fallback>
  <h3>Admin Dashboard</h3>
  <p>Admin-only content here</p>
  
  {#snippet fallbackContent()}
    <div class="text-center">
      <h3>Access Restricted</h3>
      <p>Contact your administrator for access</p>
    </div>
  {/snippet}
</PermissionCard>

<!-- Resource-based -->
<PermissionCard resource="leaders" action="moderate">
  <h3>Leader Moderation</h3>
  <p>Pending leader approvals: 5</p>
</PermissionCard>
```

## Permission Formats

### Single Permission
```typescript
permission: "resource:action" | "resource:action:scope"
```

Examples:
- `"users:read"` - Can read user data
- `"users:create"` - Can create users
- `"users:update:own"` - Can update own user data
- `"admin:access"` - Has admin access

### Multiple Permissions
```typescript
permissions: string[]
requireAll?: boolean // default: false (any permission)
```

Examples:
```svelte
<!-- User needs ANY of these permissions -->
<PermissionGuard permissions={["users:read", "admin:access"]}>
  Content
</PermissionGuard>

<!-- User needs ALL of these permissions -->
<PermissionGuard permissions={["users:read", "users:write"]} requireAll>
  Content
</PermissionGuard>
```

### Resource-Based Permissions
```typescript
resource: string
action: string
scope?: string
```

Examples:
```svelte
<PermissionGuard resource="users" action="read">
  Can read users
</PermissionGuard>

<PermissionGuard resource="users" action="update" scope="own">
  Can update own user data
</PermissionGuard>
```

## Fallback Behaviors

### PermissionGuard
- `fallback={false}` (default) - Hide content completely
- `fallback={true}` - Show fallback content or default message

### PermissionButton & PermissionLink
- `fallbackBehavior="hide"` (default) - Hide the component
- `fallbackBehavior="disable"` - Show but disable the component
- `fallbackBehavior="show"` - Show the component normally (use with caution)

## Best Practices

1. **Use specific permissions**: Prefer `"users:create"` over broad permissions like `"admin:access"`

2. **Combine with server-side checks**: These components are for UX only. Always validate permissions on the server.

3. **Handle loading states**: Use `showLoadingState` and `hideWhileLoading` for better UX during permission loading.

4. **Provide meaningful fallbacks**: When using fallback content, explain why access is restricted and how to get access.

5. **Test permission scenarios**: Test your components with different permission combinations.

## Integration with Permission Store

All components automatically integrate with the `permissionStore` from `$lib/stores/permissions`. The store handles:

- Loading user permissions from the server
- Real-time permission updates
- Caching for performance
- Error handling

Make sure to initialize the permission store in your app layout:

```svelte
<script>
  import { permissionStore } from '$lib/stores/permissions';
  import { page } from '$app/stores';
  
  // Initialize permissions when user changes
  $effect(() => {
    if ($page.data.user) {
      permissionStore.initialize($page.data.user.id);
    } else {
      permissionStore.clear();
    }
  });
</script>
```