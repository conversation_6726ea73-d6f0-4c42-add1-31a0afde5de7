<!--
  PermissionMenu Component
  
  A menu component that filters menu items based on user permissions.
  Wraps the base DropdownMenu components with permission checking.
  
  Usage:
  - Basic menu with permission-filtered items
  - Supports nested permission checks for menu items
  - Automatically hides items user doesn't have access to
-->

<script lang="ts">
	import { permissionStore } from '$lib/stores/permissions';
	import DropdownMenu from '$lib/components/ui/DropdownMenu.svelte';
	import DropdownMenuContent from '$lib/components/ui/DropdownMenuContent.svelte';
	import DropdownMenuItem from '$lib/components/ui/DropdownMenuItem.svelte';
	import DropdownMenuTrigger from '$lib/components/ui/DropdownMenuTrigger.svelte';
	import type { Snippet } from 'svelte';

	export interface PermissionMenuItem {
		id: string;
		label: string;
		icon?: string;
		
		// Permission requirements
		permission?: string;
		permissions?: string[];
		requireAll?: boolean;
		resource?: string;
		action?: string;
		scope?: string;
		
		// Action
		onclick?: () => void;
		href?: string;
		
		// Styling
		variant?: 'default' | 'destructive';
		disabled?: boolean;
		
		// Nested items
		children?: PermissionMenuItem[];
	}

	interface Props {
		items: PermissionMenuItem[];
		trigger: Snippet;
		
		// Menu positioning
		align?: 'start' | 'center' | 'end';
		side?: 'top' | 'right' | 'bottom' | 'left';
		
		// Styling
		class?: string;
		contentClass?: string;
	}

	let {
		items,
		trigger,
		align = 'end',
		side = 'bottom',
		class: className = '',
		contentClass = ''
	}: Props = $props();

	// Filter items based on permissions
	function hasItemAccess(item: PermissionMenuItem): boolean {
		// If store is not initialized, deny access by default
		if (!permissionStore.isInitialized) {
			return false;
		}

		// Resource-based permission check
		if (item.resource && item.action) {
			return permissionStore.canAccess(item.resource, item.action, item.scope);
		}

		// Single permission check
		if (item.permission) {
			return permissionStore.hasPermission(item.permission);
		}

		// Multiple permissions check
		if (item.permissions && item.permissions.length > 0) {
			return item.requireAll 
				? permissionStore.hasAllPermissions(item.permissions)
				: permissionStore.hasAnyPermission(item.permissions);
		}

		// If no permission criteria specified, allow access
		return true;
	}

	// Recursively filter menu items
	function filterItems(items: PermissionMenuItem[]): PermissionMenuItem[] {
		return items
			.filter(item => hasItemAccess(item))
			.map(item => ({
				...item,
				children: item.children ? filterItems(item.children) : undefined
			}))
			.filter(item => !item.children || item.children.length > 0); // Remove empty parent items
	}

	// Get filtered items
	const filteredItems = $derived(() => filterItems(items));

	// Handle item click
	function handleItemClick(item: PermissionMenuItem, event: MouseEvent) {
		// Double-check permissions at click time
		if (!hasItemAccess(item)) {
			event.preventDefault();
			event.stopPropagation();
			console.warn('Menu action prevented: insufficient permissions');
			return;
		}

		// Handle disabled items
		if (item.disabled) {
			event.preventDefault();
			event.stopPropagation();
			return;
		}

		// Call the item's onclick handler
		if (item.onclick) {
			item.onclick();
		}
	}
</script>

{#if filteredItems.length > 0}
	<DropdownMenu class={className}>
		<DropdownMenuTrigger>
			{@render trigger()}
		</DropdownMenuTrigger>
		
		<DropdownMenuContent {align} {side} class={contentClass}>
			{#each filteredItems as item (item.id)}
				{#if item.children && item.children.length > 0}
					<!-- Nested menu items - simplified for now -->
					<div class="px-2 py-1.5 text-sm font-medium text-gray-900 border-b">
						{item.label}
					</div>
					{#each item.children as childItem (childItem.id)}
						<DropdownMenuItem
							class="pl-4"
							disabled={childItem.disabled}
							onclick={(event) => handleItemClick(childItem, event)}
						>
							{#if childItem.icon}
								<i class={childItem.icon + ' mr-2 h-4 w-4'}></i>
							{/if}
							{childItem.label}
						</DropdownMenuItem>
					{/each}
				{:else}
					<DropdownMenuItem
						disabled={item.disabled}
						onclick={(event) => handleItemClick(item, event)}
					>
						{#if item.icon}
							<i class={item.icon + ' mr-2 h-4 w-4'}></i>
						{/if}
						{item.label}
					</DropdownMenuItem>
				{/if}
			{/each}
		</DropdownMenuContent>
	</DropdownMenu>
{/if}