import { describe, it, expect, beforeEach, vi, type Mock } from 'vitest';
import { permissionStore } from '$lib/stores/permissions.js';

// Mock the permission store
vi.mock('$lib/stores/permissions.js', () => ({
	permissionStore: {
		isLoading: false,
		isInitialized: true,
		hasPermission: vi.fn(),
		hasAnyPermission: vi.fn(),
		hasAllPermissions: vi.fn(),
		canAccess: vi.fn(),
		getError: vi.fn(() => null)
	}
}));

const mockPermissionStore = permissionStore as {
	isLoading: boolean;
	isInitialized: boolean;
	hasPermission: Mock;
	hasAnyPermission: Mock;
	hasAllPermissions: Mock;
	canAccess: Mock;
	getError: Mock;
};

// Helper functions to simulate component logic
function checkButtonAccess(props: {
	permission?: string;
	permissions?: string[];
	requireAll?: boolean;
	resource?: string;
	action?: string;
	scope?: string;
	fallback?: boolean;
	fallbackBehavior?: 'hide' | 'disable' | 'show';
}) {
	const {
		permission,
		permissions = [],
		requireAll = false,
		resource,
		action,
		scope,
		fallback = false,
		fallbackBehavior = 'hide'
	} = props;

	// If store is not initialized, deny access by default
	if (!mockPermissionStore.isInitialized) {
		return { hasAccess: false, shouldShow: false, isDisabled: false };
	}

	let hasAccess = true;

	// Resource-based permission check
	if (resource && action) {
		hasAccess = mockPermissionStore.canAccess(resource, action, scope);
	}
	// Single permission check
	else if (permission) {
		hasAccess = mockPermissionStore.hasPermission(permission);
	}
	// Multiple permissions check
	else if (permissions.length > 0) {
		hasAccess = requireAll 
			? mockPermissionStore.hasAllPermissions(permissions)
			: mockPermissionStore.hasAnyPermission(permissions);
	}

	// Determine visibility and disabled state
	const shouldShow = hasAccess || (fallback && fallbackBehavior !== 'hide');
	const isDisabled = !hasAccess && fallback && fallbackBehavior === 'disable';

	return { hasAccess, shouldShow, isDisabled };
}

function checkLinkAccess(props: {
	permission?: string;
	permissions?: string[];
	requireAll?: boolean;
	resource?: string;
	action?: string;
	scope?: string;
	fallback?: boolean;
	fallbackBehavior?: 'hide' | 'disable' | 'show';
}) {
	// Same logic as button for now
	return checkButtonAccess(props);
}

function filterMenuItems(items: Array<{
	id: string;
	permission?: string;
	permissions?: string[];
	requireAll?: boolean;
	resource?: string;
	action?: string;
	scope?: string;
	children?: any[];
}>) {
	function hasItemAccess(item: any): boolean {
		if (!mockPermissionStore.isInitialized) {
			return false;
		}

		// Resource-based permission check
		if (item.resource && item.action) {
			return mockPermissionStore.canAccess(item.resource, item.action, item.scope);
		}

		// Single permission check
		if (item.permission) {
			return mockPermissionStore.hasPermission(item.permission);
		}

		// Multiple permissions check
		if (item.permissions && item.permissions.length > 0) {
			return item.requireAll 
				? mockPermissionStore.hasAllPermissions(item.permissions)
				: mockPermissionStore.hasAnyPermission(item.permissions);
		}

		return true;
	}

	function filterRecursive(items: any[]): any[] {
		return items
			.filter(item => hasItemAccess(item))
			.map(item => ({
				...item,
				children: item.children ? filterRecursive(item.children) : undefined
			}))
			.filter(item => !item.children || item.children.length > 0);
	}

	return filterRecursive(items);
}

describe('Permission-Aware UI Components', () => {
	beforeEach(() => {
		// Reset all mocks
		vi.clearAllMocks();
		
		// Set default mock values
		mockPermissionStore.isLoading = false;
		mockPermissionStore.isInitialized = true;
		mockPermissionStore.hasPermission.mockReturnValue(false);
		mockPermissionStore.hasAnyPermission.mockReturnValue(false);
		mockPermissionStore.hasAllPermissions.mockReturnValue(false);
		mockPermissionStore.canAccess.mockReturnValue(false);
		mockPermissionStore.getError.mockReturnValue(null);
	});

	describe('PermissionButton Logic', () => {
		it('should show and enable button when user has permission', () => {
			mockPermissionStore.hasPermission.mockReturnValue(true);

			const result = checkButtonAccess({
				permission: 'users:create'
			});

			expect(result.hasAccess).toBe(true);
			expect(result.shouldShow).toBe(true);
			expect(result.isDisabled).toBe(false);
		});

		it('should hide button when user lacks permission and no fallback', () => {
			mockPermissionStore.hasPermission.mockReturnValue(false);

			const result = checkButtonAccess({
				permission: 'users:create'
			});

			expect(result.hasAccess).toBe(false);
			expect(result.shouldShow).toBe(false);
			expect(result.isDisabled).toBe(false);
		});

		it('should show disabled button when fallback behavior is disable', () => {
			mockPermissionStore.hasPermission.mockReturnValue(false);

			const result = checkButtonAccess({
				permission: 'users:create',
				fallback: true,
				fallbackBehavior: 'disable'
			});

			expect(result.hasAccess).toBe(false);
			expect(result.shouldShow).toBe(true);
			expect(result.isDisabled).toBe(true);
		});

		it('should show enabled button when fallback behavior is show', () => {
			mockPermissionStore.hasPermission.mockReturnValue(false);

			const result = checkButtonAccess({
				permission: 'users:create',
				fallback: true,
				fallbackBehavior: 'show'
			});

			expect(result.hasAccess).toBe(false);
			expect(result.shouldShow).toBe(true);
			expect(result.isDisabled).toBe(false);
		});

		it('should work with resource-based permissions', () => {
			mockPermissionStore.canAccess.mockReturnValue(true);

			const result = checkButtonAccess({
				resource: 'users',
				action: 'create'
			});

			expect(result.hasAccess).toBe(true);
			expect(result.shouldShow).toBe(true);
			expect(result.isDisabled).toBe(false);
			expect(mockPermissionStore.canAccess).toHaveBeenCalledWith('users', 'create', undefined);
		});

		it('should work with multiple permissions (any)', () => {
			mockPermissionStore.hasAnyPermission.mockReturnValue(true);

			const result = checkButtonAccess({
				permissions: ['users:create', 'admin:access']
			});

			expect(result.hasAccess).toBe(true);
			expect(result.shouldShow).toBe(true);
			expect(result.isDisabled).toBe(false);
			expect(mockPermissionStore.hasAnyPermission).toHaveBeenCalledWith(['users:create', 'admin:access']);
		});

		it('should work with multiple permissions (all required)', () => {
			mockPermissionStore.hasAllPermissions.mockReturnValue(true);

			const result = checkButtonAccess({
				permissions: ['users:create', 'admin:access'],
				requireAll: true
			});

			expect(result.hasAccess).toBe(true);
			expect(result.shouldShow).toBe(true);
			expect(result.isDisabled).toBe(false);
			expect(mockPermissionStore.hasAllPermissions).toHaveBeenCalledWith(['users:create', 'admin:access']);
		});
	});

	describe('PermissionLink Logic', () => {
		it('should show and enable link when user has permission', () => {
			mockPermissionStore.hasPermission.mockReturnValue(true);

			const result = checkLinkAccess({
				permission: 'users:read'
			});

			expect(result.hasAccess).toBe(true);
			expect(result.shouldShow).toBe(true);
			expect(result.isDisabled).toBe(false);
		});

		it('should hide link when user lacks permission and no fallback', () => {
			mockPermissionStore.hasPermission.mockReturnValue(false);

			const result = checkLinkAccess({
				permission: 'users:read'
			});

			expect(result.hasAccess).toBe(false);
			expect(result.shouldShow).toBe(false);
			expect(result.isDisabled).toBe(false);
		});

		it('should show disabled link when fallback behavior is disable', () => {
			mockPermissionStore.hasPermission.mockReturnValue(false);

			const result = checkLinkAccess({
				permission: 'users:read',
				fallback: true,
				fallbackBehavior: 'disable'
			});

			expect(result.hasAccess).toBe(false);
			expect(result.shouldShow).toBe(true);
			expect(result.isDisabled).toBe(true);
		});
	});

	describe('PermissionMenu Logic', () => {
		it('should filter out items user cannot access', () => {
			mockPermissionStore.hasPermission.mockImplementation((permission: string) => {
				return permission === 'users:read';
			});

			const items = [
				{ id: '1', permission: 'users:read', label: 'View Users' },
				{ id: '2', permission: 'users:create', label: 'Create User' },
				{ id: '3', permission: 'admin:access', label: 'Admin Panel' }
			];

			const filtered = filterMenuItems(items);

			expect(filtered).toHaveLength(1);
			expect(filtered[0].id).toBe('1');
			expect(mockPermissionStore.hasPermission).toHaveBeenCalledWith('users:read');
			expect(mockPermissionStore.hasPermission).toHaveBeenCalledWith('users:create');
			expect(mockPermissionStore.hasPermission).toHaveBeenCalledWith('admin:access');
		});

		it('should handle nested menu items', () => {
			mockPermissionStore.hasPermission.mockImplementation((permission: string) => {
				return permission === 'users:read' || permission === 'users:update';
			});

			const items = [
				{
					id: '1',
					permission: 'users:read',
					label: 'Users',
					children: [
						{ id: '1-1', permission: 'users:update', label: 'Edit Users' },
						{ id: '1-2', permission: 'users:delete', label: 'Delete Users' }
					]
				}
			];

			const filtered = filterMenuItems(items);

			expect(filtered).toHaveLength(1);
			expect(filtered[0].children).toHaveLength(1);
			expect(filtered[0].children[0].id).toBe('1-1');
		});

		it('should remove parent items with no accessible children', () => {
			mockPermissionStore.hasPermission.mockReturnValue(false);

			const items = [
				{
					id: '1',
					permission: 'users:read',
					label: 'Users',
					children: [
						{ id: '1-1', permission: 'users:update', label: 'Edit Users' },
						{ id: '1-2', permission: 'users:delete', label: 'Delete Users' }
					]
				}
			];

			const filtered = filterMenuItems(items);

			expect(filtered).toHaveLength(0);
		});

		it('should work with resource-based permissions', () => {
			mockPermissionStore.canAccess.mockReturnValue(true);

			const items = [
				{ id: '1', resource: 'users', action: 'read', label: 'View Users' }
			];

			const filtered = filterMenuItems(items);

			expect(filtered).toHaveLength(1);
			expect(mockPermissionStore.canAccess).toHaveBeenCalledWith('users', 'read', undefined);
		});

		it('should work with multiple permissions', () => {
			mockPermissionStore.hasAnyPermission.mockReturnValue(true);

			const items = [
				{ id: '1', permissions: ['users:read', 'admin:access'], label: 'Users or Admin' }
			];

			const filtered = filterMenuItems(items);

			expect(filtered).toHaveLength(1);
			expect(mockPermissionStore.hasAnyPermission).toHaveBeenCalledWith(['users:read', 'admin:access']);
		});
	});

	describe('Store Initialization', () => {
		it('should deny access when store is not initialized', () => {
			mockPermissionStore.isInitialized = false;
			mockPermissionStore.hasPermission.mockReturnValue(true);

			const buttonResult = checkButtonAccess({
				permission: 'users:read'
			});

			const linkResult = checkLinkAccess({
				permission: 'users:read'
			});

			const menuItems = filterMenuItems([
				{ id: '1', permission: 'users:read', label: 'Users' }
			]);

			expect(buttonResult.hasAccess).toBe(false);
			expect(linkResult.hasAccess).toBe(false);
			expect(menuItems).toHaveLength(0);
		});
	});

	describe('Permission Priority', () => {
		it('should prioritize resource-based permissions over single permissions', () => {
			mockPermissionStore.canAccess.mockReturnValue(true);
			mockPermissionStore.hasPermission.mockReturnValue(false);

			const result = checkButtonAccess({
				permission: 'users:read',
				resource: 'users',
				action: 'read'
			});

			expect(result.hasAccess).toBe(true);
			expect(mockPermissionStore.canAccess).toHaveBeenCalledWith('users', 'read', undefined);
			expect(mockPermissionStore.hasPermission).not.toHaveBeenCalled();
		});

		it('should prioritize single permissions over multiple permissions', () => {
			mockPermissionStore.hasPermission.mockReturnValue(true);
			mockPermissionStore.hasAnyPermission.mockReturnValue(false);

			const result = checkButtonAccess({
				permission: 'users:read',
				permissions: ['users:write']
			});

			expect(result.hasAccess).toBe(true);
			expect(mockPermissionStore.hasPermission).toHaveBeenCalledWith('users:read');
			expect(mockPermissionStore.hasAnyPermission).not.toHaveBeenCalled();
		});
	});
});