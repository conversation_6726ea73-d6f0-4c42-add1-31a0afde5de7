<!--
  PermissionLink Component
  
  A link component that conditionally renders based on user permissions.
  Can render as a regular anchor tag or SvelteKit link.
  
  Usage:
  - Basic: <PermissionLink permission="users:read" href="/users">Users</PermissionLink>
  - Multiple permissions: <PermissionLink permissions={["users:read", "admin:access"]} href="/admin">Admin</PermissionLink>
  - Resource-based: <PermissionLink resource="users" action="read" href="/users">Users</PermissionLink>
  - With fallback: <PermissionLink permission="admin:access" href="/admin" fallback disabled>Admin (No Access)</PermissionLink>
-->

<script lang="ts">
	import { permissionStore } from '$lib/stores/permissions';
	import type { Snippet } from 'svelte';

	interface Props {
		// Permission checking props
		permission?: string;
		permissions?: string[];
		requireAll?: boolean;
		resource?: string;
		action?: string;
		scope?: string;
		
		// Fallback behavior
		fallback?: boolean;
		fallbackBehavior?: 'hide' | 'disable' | 'show';
		
		// Link props
		href: string;
		target?: string;
		rel?: string;
		
		// Styling
		class?: string;
		activeClass?: string;
		
		// SvelteKit specific
		prefetch?: 'hover' | 'tap' | 'off';
		
		// Event handlers
		onclick?: (event: MouseEvent) => void;
		
		// Content
		children: Snippet;
	}

	let {
		permission,
		permissions = [],
		requireAll = false,
		resource,
		action,
		scope,
		fallback = false,
		fallbackBehavior = 'hide',
		href,
		target,
		rel,
		class: className = '',
		activeClass = '',
		prefetch = 'hover',
		onclick,
		children
	}: Props = $props();

	// Reactive permission check using $derived
	const hasAccess = $derived(() => {
		// If store is not initialized, deny access by default
		if (!$permissionStore.initialized) {
			return false;
		}

		// Resource-based permission check
		if (resource && action) {
			return permissionStore.canAccess(resource, action, scope);
		}

		// Single permission check
		if (permission) {
			return permissionStore.hasPermission(permission);
		}

		// Multiple permissions check
		if (permissions.length > 0) {
			return requireAll
				? permissionStore.hasAllPermissions(permissions)
				: permissionStore.hasAnyPermission(permissions);
		}

		// If no permission criteria specified, allow access
		return true;
	});

	// Determine if link should be shown
	const shouldShow = $derived(() => {
		if (hasAccess) return true;
		if (!fallback) return false;
		return fallbackBehavior !== 'hide';
	});

	// Determine if link should be disabled
	const isDisabled = $derived(() => {
		if (hasAccess) return false;
		return fallback && fallbackBehavior === 'disable';
	});

	// Compute final class names
	const finalClassName = $derived(() => {
		let classes = className;
		
		if (isDisabled) {
			classes += ' opacity-50 cursor-not-allowed pointer-events-none';
		}
		
		return classes;
	});

	// Handle click with permission check
	function handleClick(event: MouseEvent) {
		// Prevent navigation if no permission and not in fallback mode
		if (!hasAccess && (!fallback || fallbackBehavior === 'hide')) {
			event.preventDefault();
			event.stopPropagation();
			console.warn('Navigation prevented: insufficient permissions');
			return;
		}

		// Prevent navigation if disabled
		if (isDisabled) {
			event.preventDefault();
			event.stopPropagation();
			return;
		}

		// Call the provided onclick handler
		if (onclick) {
			onclick(event);
		}
	}
</script>

{#if shouldShow}
	{#if isDisabled}
		<span class={finalClassName}>
			{@render children()}
		</span>
	{:else}
		<a
			{href}
			{target}
			{rel}
			class={finalClassName}
			data-sveltekit-prefetch={prefetch}
			onclick={handleClick}
		>
			{@render children()}
		</a>
	{/if}
{/if}