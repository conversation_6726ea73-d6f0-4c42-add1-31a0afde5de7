<!-- Leader Profile Header - Matches React LeaderProfileHeader.jsx exactly -->
<script lang="ts">
	import Avatar from '$lib/components/ui/Avatar.svelte';

	// Define a proper type for the leader object for better type safety
	interface Leader {
		name: string;
		position: string;
		party: string;
		state: string;
		avatarUrl?: string;
		coverImageUrl?: string;
		isVerified?: boolean;
	}

	interface Props {
		leader: Leader;
	}

	let { leader }: Props = $props();

	// Helper functions
	function getPartyColor(party: string) {
		const colors: Record<string, string> = {
			'APC': 'bg-blue-500',
			'PDP': 'bg-green-500',
			'LP': 'bg-red-500',
			'NNPP': 'bg-orange-500'
		};
		return colors[party] || 'bg-gray-500';
	}

	function getInitials(name: string) {
		if (!name) return '';
		const parts = name.split(' ');
		return (parts.length > 1 ? parts[0][0] + parts[parts.length - 1][0] : name.substring(0, 2)).toUpperCase();
	}

	function getAvatarUrl(leader: Leader) {
		return leader.avatarUrl || `https://avatar.vercel.sh/${leader.name.replace(/\s/g, "")}.png?size=160`;
	}

	function hasCoverImage(leader: Leader) {
		return leader.coverImageUrl && leader.coverImageUrl.trim() !== '';
	}
</script>

{#if leader}
	<div class="relative">
		<!-- Background - Cover image or gradient -->
		<div class="h-48 md:h-64 w-full rounded-xl md:rounded-2xl overflow-hidden shadow-2xl relative">
			{#if hasCoverImage(leader)}
				<!-- Cover Image -->
				<img
					src={leader.coverImageUrl}
					alt="{leader.name} cover"
					class="w-full h-full object-cover"
				/>
				<!-- Overlay for better text readability -->
				<div class="absolute inset-0 bg-black/30"></div>
			{:else}
				<!-- Fallback gradient -->
				<div class="w-full h-full bg-gradient-to-br from-primary/70 via-accent/50 to-secondary/60
							dark:from-primary/40 dark:via-accent/30 dark:to-secondary/40">
				</div>
			{/if}
		</div>

		<div class="absolute inset-0 flex flex-col justify-end p-4 md:p-0">
			<div class="md:flex md:items-end md:space-x-6 w-full max-w-5xl mx-auto md:px-8">
				<!-- Avatar section matching React exactly -->
				<div class="relative -mb-12 md:-mb-0 md:mb-[-3rem] z-10 mx-auto md:mx-0">
					<Avatar
						className="h-32 w-32 md:h-40 md:w-40 border-4 border-card shadow-xl bg-secondary rounded-full"
						src={getAvatarUrl(leader)}
						alt={leader.name}
						fallback={getInitials(leader.name)}
					/>
					{#if leader.isVerified}
						<div class="absolute bottom-1 right-1 h-8 w-8 text-blue-500 fill-white bg-card rounded-full p-0.5">
							<svg class="h-full w-full" fill="currentColor" viewBox="0 0 24 24">
								<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
							</svg>
						</div>
					{/if}
				</div>

				<!-- Info section matching React exactly -->
				<div class="flex-grow min-w-0 mt-4 md:mt-0 md:pb-6 md:pl-4
							bg-card/80 dark:bg-card/70 backdrop-blur-md shadow-xl rounded-xl md:rounded-t-xl md:rounded-b-none
							p-4 pt-16 md:pt-6 text-center md:text-left">
					<h1 class="text-2xl sm:text-3xl md:text-4xl font-extrabold text-foreground truncate flex items-center justify-center md:justify-start" title={leader.name}>
						{leader.name}
					</h1>
					<p class="text-base md:text-lg text-muted-foreground mt-1 truncate" title={leader.position}>
						{leader.position}
					</p>
					<div class="flex items-center justify-center md:justify-start space-x-3 mt-2">
						<span class="px-3 py-1 text-xs font-semibold rounded-full text-white {getPartyColor(leader.party)}">
							{leader.party}
						</span>
						<span class="text-sm text-muted-foreground truncate" title={leader.state}>
							{leader.state} State
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
{/if}