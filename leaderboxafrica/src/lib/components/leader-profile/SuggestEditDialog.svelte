<!-- Suggest Edit Dialog - Placeholder -->
<script lang="ts">
	import Button from '$lib/components/ui/Button.svelte';

	interface Props {
		isOpen?: boolean;
		onClose: () => void;
		leader: any;
		onSubmit: (data: { field: string; suggestion: string }) => void;
	}

	let { isOpen = $bindable(false), onClose, leader, onSubmit }: Props = $props();

	let suggestion = $state('');
	let field = $state('bio');
</script>

{#if isOpen}
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
		<div class="bg-white rounded-lg p-6 max-w-md w-full">
			<h3 class="text-lg font-semibold text-gray-900 mb-4">Suggest Edit for {leader?.name}</h3>
			
			<div class="mb-4">
				<label class="block text-sm font-medium text-gray-700 mb-2">Field to Edit</label>
				<select bind:value={field} class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary">
					<option value="bio">Biography</option>
					<option value="education">Education</option>
					<option value="background">Background</option>
					<option value="position">Position</option>
				</select>
			</div>
			
			<div class="mb-6">
				<label class="block text-sm font-medium text-gray-700 mb-2">Suggested Change</label>
				<textarea
					bind:value={suggestion}
					placeholder="Describe your suggested edit..."
					class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
					rows="4"
				></textarea>
			</div>

			<div class="flex space-x-3">
				<Button 
					disabled={!suggestion.trim()} 
					on:click={() => onSubmit({ field, suggestion })}
					className="flex-1"
				>
					Submit Suggestion
				</Button>
				<Button variant="outline" on:click={onClose} className="flex-1">
					Cancel
				</Button>
			</div>
		</div>
	</div>
{/if}
