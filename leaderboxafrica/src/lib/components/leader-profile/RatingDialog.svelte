<!-- Rating Dialog - Placeholder -->
<script lang="ts">
	import Button from '$lib/components/ui/Button.svelte';

	interface Props {
		isOpen?: boolean;
		onClose: () => void;
		leaderName: string;
		currentRating?: number;
		setCurrentRating: (rating: number) => void;
		commentText?: string;
		setCommentText: (text: string) => void;
		hoverRating?: number;
		setHoverRating: (rating: number) => void;
		onSubmit: () => void;
	}

	let {
		isOpen = $bindable(false),
		onClose,
		leaderName,
		currentRating = $bindable(0),
		setCurrentRating,
		commentText = $bindable(''),
		setCommentText,
		hoverRating = $bindable(0),
		setHoverRating,
		onSubmit
	}: Props = $props();
</script>

{#if isOpen}
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
		<div class="bg-white rounded-lg p-6 max-w-md w-full">
			<h3 class="text-lg font-semibold text-gray-900 mb-4">Rate {leaderName}</h3>
			
			<!-- Star Rating -->
			<div class="mb-4">
				<label class="block text-sm font-medium text-gray-700 mb-2">Your Rating</label>
				<div class="flex space-x-1">
					{#each Array(5) as _, i}
						<button
							class="w-8 h-8 {i < currentRating ? 'text-yellow-400' : 'text-gray-300'} hover:text-yellow-400 transition-colors"
							onclick={() => setCurrentRating(i + 1)}
							onmouseenter={() => setHoverRating(i + 1)}
							onmouseleave={() => setHoverRating(0)}
						>
							<svg fill="currentColor" viewBox="0 0 20 20">
								<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
							</svg>
						</button>
					{/each}
				</div>
			</div>

			<!-- Comment -->
			<div class="mb-6">
				<label class="block text-sm font-medium text-gray-700 mb-2">Comment (Optional)</label>
				<textarea
					bind:value={commentText}
					oninput={(e) => setCommentText(e.target.value)}
					placeholder="Share your thoughts about this leader..."
					class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
					rows="3"
				></textarea>
			</div>

			<div class="flex space-x-3">
				<Button onclick={onSubmit} disabled={currentRating === 0} className="flex-1">
					Submit Rating
				</Button>
				<Button variant="outline" onclick={onClose} className="flex-1">
					Cancel
				</Button>
			</div>
		</div>
	</div>
{/if}
