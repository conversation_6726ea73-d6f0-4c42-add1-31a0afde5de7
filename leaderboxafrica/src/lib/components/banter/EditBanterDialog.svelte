<!-- Edit Banter Dialog Component - Svelte 5 conversion of React EditBanterDialog.jsx -->
<script lang="ts">
	import Dialog from '$lib/components/ui/Dialog.svelte';
	import DialogHeader from '$lib/components/ui/DialogHeader.svelte';
	import DialogTitle from '$lib/components/ui/DialogTitle.svelte';
	import DialogDescription from '$lib/components/ui/DialogDescription.svelte';
	import DialogFooter from '$lib/components/ui/DialogFooter.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Textarea from '$lib/components/ui/Textarea.svelte';
	import Label from '$lib/components/ui/Label.svelte';
	import Combobox from '$lib/components/ui/Combobox.svelte';
	import MediaUpload from '$lib/components/ui/MediaUpload.svelte';
	import { Edit3 } from 'lucide-svelte';
	import { createEventDispatcher } from 'svelte';

	// Props
	let { 
		isOpen = $bindable(false),
		onClose,
		onSubmit,
		banter,
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// State
	let formData = $state({
		title: '',
		details: '',
		tags: {
			leader: '',
			location: '',
			party: '',
			topic: '',
		},
		sourceLink: '',
		media: [],
	});

	// Mock data - in real app, these would come from stores
	let leaderOptions = $state([
		{ value: 'John Doe', label: 'John Doe' },
		{ value: 'Jane Smith', label: 'Jane Smith' },
		{ value: 'Bob Johnson', label: 'Bob Johnson' }
	]);

	let stateOptions = $state([
		{ value: 'California', label: 'California' },
		{ value: 'Texas', label: 'Texas' },
		{ value: 'New York', label: 'New York' },
		{ value: 'Florida', label: 'Florida' }
	]);

	let partyOptions = $state([
		{ value: 'Democratic', label: 'Democratic' },
		{ value: 'Republican', label: 'Republican' },
		{ value: 'Independent', label: 'Independent' },
		{ value: 'Green', label: 'Green' },
		{ value: 'Libertarian', label: 'Libertarian' }
	]);

	// Effects
	$effect(() => {
		if (banter && isOpen) {
			formData = {
				title: banter.title || '',
				details: banter.details || '',
				tags: {
					leader: banter.tags?.leader || '',
					location: banter.tags?.location || '',
					party: banter.tags?.party || '',
					topic: banter.tags?.topic || '',
				},
				sourceLink: banter.sourceLink || '',
				media: banter.media || [],
			};
		}
	});

	// Handle form field changes
	function handleChange(field: string, value: string) {
		formData = { ...formData, [field]: value };
	}

	// Handle tag changes
	function handleTagChange(tagName: string, value: string) {
		formData = {
			...formData,
			tags: { ...formData.tags, [tagName]: value }
		};
	}

	// Handle media changes
	function handleMediaChange(files: File[]) {
		const mediaFiles = files.map(file => ({
			name: file.name,
			type: file.type,
			size: file.size,
			url: URL.createObjectURL(file)
		}));
		formData = { ...formData, media: mediaFiles };
	}

	// Handle form submission
	function handleSubmit(e: Event) {
		e.preventDefault();
		
		if (!formData.title?.trim() || !formData.details?.trim()) {
			dispatch('toast', {
				title: "Error",
				description: "Title and details cannot be empty.",
				variant: "destructive"
			});
			return;
		}

		onSubmit?.(formData);
		dispatch('submit', formData);
		handleClose();
	}

	// Handle close
	function handleClose() {
		isOpen = false;
		onClose?.();
		dispatch('close');
	}
</script>

{#if banter}
	<Dialog bind:open={isOpen} onOpenChange={handleClose}>
		<div 
			class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg md:max-w-xl"
			{...restProps}
		>
			<DialogHeader>
				<DialogTitle class="text-primary text-2xl flex items-center">
					<Edit3 size={24} class="mr-2" /> 
					Edit Your Banter
				</DialogTitle>
				<DialogDescription>
					Refine your post. You can edit the content and tags of your banter here.
				</DialogDescription>
			</DialogHeader>

			<form on:submit={handleSubmit} class="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-3 text-sm">
				<!-- Title -->
				<div>
					<Label for="edit-banter-title" class="text-foreground font-semibold">Banter Title</Label>
					<Input
						id="edit-banter-title"
						value={formData.title}
						on:input={(e) => handleChange('title', e.target.value)}
						class="modern-input mt-1"
						required
					/>
				</div>

				<!-- Details -->
				<div>
					<Label for="edit-banter-details" class="text-foreground font-semibold">Details</Label>
					<Textarea
						id="edit-banter-details"
						value={formData.details}
						on:input={(e) => handleChange('details', e.target.value)}
						class="modern-input mt-1 min-h-[120px]"
						required
					/>
				</div>

				<!-- Tags Grid -->
				<div class="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-2">
					<!-- Leader Tag -->
					<div>
						<Label>Tag Leader</Label>
						<Combobox 
							options={leaderOptions} 
							value={formData.tags?.leader} 
							onChange={(val) => handleTagChange('leader', val)} 
							placeholder="Select leader..."
						/>
					</div>

					<!-- Location Tag -->
					<div>
						<Label>Tag State</Label>
						<Combobox 
							options={stateOptions} 
							value={formData.tags?.location} 
							onChange={(val) => handleTagChange('location', val)} 
							placeholder="Select state..."
						/>
					</div>

					<!-- Party Tag -->
					<div>
						<Label>Tag Party</Label>
						<Combobox 
							options={partyOptions} 
							value={formData.tags?.party} 
							onChange={(val) => handleTagChange('party', val)} 
							placeholder="Select party..."
						/>
					</div>

					<!-- Topic Tag -->
					<div>
						<Label>Tag Topic</Label>
						<Input 
							value={formData.tags?.topic || ''} 
							on:input={(e) => handleTagChange('topic', e.target.value)} 
							placeholder="e.g. Economy" 
						/>
					</div>
				</div>

				<!-- Source Link -->
				<div>
					<Label>Source Link</Label>
					<Input 
						value={formData.sourceLink || ''} 
						on:input={(e) => handleChange('sourceLink', e.target.value)} 
						placeholder="https://example.com/article" 
					/>
				</div>

				<!-- Media -->
				<div>
					<Label>Media</Label>
					<MediaUpload 
						onFilesChange={handleMediaChange} 
						existingFiles={formData.media} 
						maxFiles={5}
						maxFileSizeMB={10}
					/>
				</div>
			</form>

			<DialogFooter class="mt-2">
				<Button type="button" variant="outline" on:click={handleClose}>
					Cancel
				</Button>
				<Button type="submit" on:click={handleSubmit} class="btn-primary">
					Update Banter
				</Button>
			</DialogFooter>
		</div>
	</Dialog>
{/if}
