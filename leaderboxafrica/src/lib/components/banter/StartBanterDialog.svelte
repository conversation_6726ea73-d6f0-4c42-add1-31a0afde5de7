<!-- Start Banter Dialog Component - Svelte 5 conversion of React StartBanterDialog.jsx -->
<script lang="ts">
	import Dialog from '$lib/components/ui/Dialog.svelte';
	import DialogHeader from '$lib/components/ui/DialogHeader.svelte';
	import DialogTitle from '$lib/components/ui/DialogTitle.svelte';
	import DialogDescription from '$lib/components/ui/DialogDescription.svelte';
	import DialogFooter from '$lib/components/ui/DialogFooter.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Textarea from '$lib/components/ui/Textarea.svelte';
	import Label from '$lib/components/ui/Label.svelte';
	import Combobox from '$lib/components/ui/Combobox.svelte';
	import MediaUpload from '$lib/components/ui/MediaUpload.svelte';
	import { Tag, Users, MapPin, Flag, BookOpen, Link as LinkIcon } from 'lucide-svelte';
	import { createEventDispatcher } from 'svelte';

	// Props
	let { 
		isOpen = $bindable(false),
		onClose,
		onSubmit,
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// State
	let title = $state('');
	let details = $state('');
	let tagLeader = $state('');
	let tagLocation = $state('');
	let tagParty = $state('');
	let tagTopic = $state('');
	let sourceLink = $state('');
	let attachedMedia = $state([]);

	// Mock data - in real app, these would come from stores
	let leaderOptions = $state([
		{ value: 'John Doe', label: 'John Doe' },
		{ value: 'Jane Smith', label: 'Jane Smith' },
		{ value: 'Bob Johnson', label: 'Bob Johnson' }
	]);

	let stateOptions = $state([
		{ value: 'California', label: 'California' },
		{ value: 'Texas', label: 'Texas' },
		{ value: 'New York', label: 'New York' },
		{ value: 'Florida', label: 'Florida' }
	]);

	let partyOptions = $state([
		{ value: 'Democratic', label: 'Democratic' },
		{ value: 'Republican', label: 'Republican' },
		{ value: 'Independent', label: 'Independent' },
		{ value: 'Green', label: 'Green' },
		{ value: 'Libertarian', label: 'Libertarian' }
	]);

	// Handle form submission
	function handleSubmit(e: Event) {
		e.preventDefault();
		
		if (!title.trim() || !details.trim()) {
			dispatch('toast', {
				title: "Error",
				description: "Title and Details are required.",
				variant: "destructive"
			});
			return;
		}

		const formData = {
			title,
			details,
			tags: {
				leader: tagLeader,
				location: tagLocation,
				party: tagParty,
				topic: tagTopic,
			},
			sourceLink,
			media: attachedMedia,
		};

		onSubmit?.(formData);
		dispatch('submit', formData);
		resetForm();
		handleClose();
	}

	// Handle close
	function handleClose() {
		isOpen = false;
		onClose?.();
		dispatch('close');
	}

	// Reset form
	function resetForm() {
		title = '';
		details = '';
		tagLeader = '';
		tagLocation = '';
		tagParty = '';
		tagTopic = '';
		sourceLink = '';
		attachedMedia = [];
	}

	// Handle media change
	function handleMediaChange(files: File[]) {
		attachedMedia = files;
	}
</script>

<Dialog bind:open={isOpen} onOpenChange={handleClose}>
	<div 
		class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg md:max-w-xl"
		{...restProps}
	>
		<DialogHeader>
			<DialogTitle class="text-primary text-2xl flex items-center">
				<Tag size={24} class="mr-2" /> 
				Start a New Banter
			</DialogTitle>
			<DialogDescription>
				Ignite the conversation! Share your thoughts, questions, or observations. Keep it civil and constructive.
			</DialogDescription>
		</DialogHeader>

		<form on:submit={handleSubmit} class="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-3 text-sm">
			<!-- Title -->
			<div>
				<Label for="banter-title" class="text-foreground font-semibold">Banter Title</Label>
				<Input
					id="banter-title"
					bind:value={title}
					placeholder="What's on your mind?"
					class="modern-input mt-1"
					required
				/>
			</div>

			<!-- Details -->
			<div>
				<Label for="banter-details" class="text-foreground font-semibold">Details</Label>
				<Textarea
					id="banter-details"
					bind:value={details}
					placeholder="Share your thoughts, provide context, ask questions..."
					class="modern-input mt-1 min-h-[120px]"
					required
				/>
			</div>

			<!-- Tags Grid -->
			<div class="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-2">
				<!-- Leader Tag -->
				<div>
					<Label for="tag-leader" class="text-foreground flex items-center">
						<Users size={14} class="mr-1.5 text-muted-foreground" /> 
						Tag Leader (Optional)
					</Label>
					<Combobox
						options={leaderOptions}
						bind:value={tagLeader}
						placeholder="Select or type leader..."
						inputClassName="modern-input mt-1"
					/>
				</div>

				<!-- Location Tag -->
				<div>
					<Label for="tag-location" class="text-foreground flex items-center">
						<MapPin size={14} class="mr-1.5 text-muted-foreground" /> 
						Tag State/Location (Optional)
					</Label>
					<Combobox
						options={stateOptions}
						bind:value={tagLocation}
						placeholder="Select or type state..."
						inputClassName="modern-input mt-1"
					/>
				</div>

				<!-- Party Tag -->
				<div>
					<Label for="tag-party" class="text-foreground flex items-center">
						<Flag size={14} class="mr-1.5 text-muted-foreground" /> 
						Tag Party (Optional)
					</Label>
					<Combobox
						options={partyOptions}
						bind:value={tagParty}
						placeholder="Select or type party..."
						inputClassName="modern-input mt-1"
					/>
				</div>

				<!-- Topic Tag -->
				<div>
					<Label for="tag-topic" class="text-foreground flex items-center">
						<BookOpen size={14} class="mr-1.5 text-muted-foreground" /> 
						Tag Topic (Optional)
					</Label>
					<Input
						id="tag-topic"
						bind:value={tagTopic}
						placeholder="e.g. Economy, Healthcare"
						class="modern-input mt-1"
					/>
				</div>
			</div>

			<!-- Source Link -->
			<div>
				<Label for="source-link" class="text-foreground flex items-center">
					<LinkIcon size={14} class="mr-1.5 text-muted-foreground" /> 
					Source Link (Optional)
				</Label>
				<Input
					id="source-link"
					bind:value={sourceLink}
					placeholder="https://example.com/article"
					class="modern-input mt-1"
				/>
			</div>

			<!-- Media Upload -->
			<div>
				<Label class="text-foreground font-semibold">Attach Media (Optional)</Label>
				<MediaUpload 
					onFilesChange={handleMediaChange}
					existingFiles={attachedMedia}
					maxFiles={5}
					maxFileSizeMB={10}
				/>
			</div>

			<p class="text-xs text-muted-foreground mt-2">
				Tags help others find your banter. Use relevant keywords.
			</p>
		</form>

		<DialogFooter class="mt-2">
			<Button type="button" variant="outline" on:click={handleClose}>
				Cancel
			</Button>
			<Button type="submit" on:click={handleSubmit} class="btn-primary">
				Submit Banter
			</Button>
		</DialogFooter>
	</div>
</Dialog>
