<!-- Start Discussion Dialog Component - Svelte 5 conversion of React StartDiscussionDialog.jsx -->
<script lang="ts">
	import Dialog from '$lib/components/ui/Dialog.svelte';
	import DialogHeader from '$lib/components/ui/DialogHeader.svelte';
	import DialogTitle from '$lib/components/ui/DialogTitle.svelte';
	import DialogDescription from '$lib/components/ui/DialogDescription.svelte';
	import DialogFooter from '$lib/components/ui/DialogFooter.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Textarea from '$lib/components/ui/Textarea.svelte';
	import Label from '$lib/components/ui/Label.svelte';
	import MediaUpload from '$lib/components/ui/MediaUpload.svelte';
	import { MessageSquare, Paperclip } from 'lucide-svelte';
	import { createEventDispatcher } from 'svelte';

	// Props
	let { 
		isOpen = $bindable(false),
		onClose,
		onSubmit,
		groupId = '',
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// State
	let title = $state('');
	let content = $state('');
	let attachedMedia = $state([]);

	// Handle media change
	function handleMediaChange(files: File[]) {
		const mediaFiles = files.map(file => ({
			name: file.name,
			type: file.type,
			size: file.size,
			url: URL.createObjectURL(file)
		}));
		attachedMedia = mediaFiles;
	}

	// Handle form submission
	function handleSubmit() {
		if (!title.trim() || !content.trim()) {
			dispatch('toast', {
				title: 'Missing Information',
				description: 'Please provide both a title and content for your discussion.',
				variant: 'destructive',
			});
			return;
		}

		const discussionData = { 
			title, 
			content, 
			media: attachedMedia,
			groupId 
		};

		onSubmit?.(discussionData);
		dispatch('submit', discussionData);
		resetForm();
		handleClose();
	}

	// Handle close
	function handleClose() {
		isOpen = false;
		onClose?.();
		dispatch('close');
	}

	// Reset form
	function resetForm() {
		title = '';
		content = '';
		attachedMedia = [];
	}
</script>

<Dialog bind:open={isOpen} onOpenChange={handleClose}>
	<div 
		class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg"
		{...restProps}
	>
		<DialogHeader>
			<DialogTitle class="text-primary text-2xl flex items-center">
				<MessageSquare size={24} class="mr-2" /> 
				Start a New Discussion
			</DialogTitle>
			<DialogDescription>
				Share your topic with the group. Keep it relevant and constructive.
			</DialogDescription>
		</DialogHeader>

		<div class="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-3 text-sm">
			<!-- Discussion Title -->
			<div>
				<Label for="discussion-title" class="font-semibold">Discussion Title</Label>
				<Input 
					id="discussion-title" 
					bind:value={title} 
					class="modern-input mt-1" 
					placeholder="What would you like to discuss?"
				/>
			</div>

			<!-- Discussion Content -->
			<div>
				<Label for="discussion-content" class="font-semibold">Your Post</Label>
				<Textarea 
					id="discussion-content" 
					bind:value={content} 
					class="modern-input min-h-[120px] mt-1"
					placeholder="Elaborate on your topic, ask a question, or share your perspective..."
				/>
			</div>

			<!-- Media Upload -->
			<div>
				<Label for="discussion-media" class="text-foreground font-semibold flex items-center">
					<Paperclip size={14} class="mr-1.5 text-muted-foreground" /> 
					Attach Media (Optional)
				</Label>
				<MediaUpload 
					onFilesChange={handleMediaChange}
					existingFiles={attachedMedia}
					maxFiles={5}
					maxFileSizeMB={10}
				/>
			</div>
		</div>

		<DialogFooter class="mt-2">
			<Button type="button" variant="outline" on:click={handleClose}>
				Cancel
			</Button>
			<Button type="button" on:click={handleSubmit} class="btn-primary">
				Post Discussion
			</Button>
		</DialogFooter>
	</div>
</Dialog>
