<!-- Create Group Dialog Component - Svelte 5 conversion of React CreateGroupDialog.jsx -->
<script lang="ts">
	import Dialog from '$lib/components/ui/Dialog.svelte';
	import DialogHeader from '$lib/components/ui/DialogHeader.svelte';
	import DialogTitle from '$lib/components/ui/DialogTitle.svelte';
	import DialogDescription from '$lib/components/ui/DialogDescription.svelte';
	import DialogFooter from '$lib/components/ui/DialogFooter.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Textarea from '$lib/components/ui/Textarea.svelte';
	import Label from '$lib/components/ui/Label.svelte';
	import Switch from '$lib/components/ui/Switch.svelte';
	import { Users, Globe, Lock } from 'lucide-svelte';
	import { createEventDispatcher } from 'svelte';

	// Props
	let { 
		isOpen = $bindable(false),
		onClose,
		onSubmit,
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// State
	let formData = $state({
		name: '',
		description: '',
		isPublic: true,
		tags: { 
			leader: '', 
			location: '', 
			party: '', 
			keywords: [] as string[]
		}
	});

	// Handle form field changes
	function handleChange(field: string, value: string) {
		if (field.startsWith("tag-")) {
			const tagName = field.split("-")[1];
			if (tagName === 'keywords') {
				const keywords = value.split(',').map(kw => kw.trim()).filter(Boolean);
				formData = {
					...formData,
					tags: { ...formData.tags, keywords }
				};
			} else {
				formData = {
					...formData,
					tags: { ...formData.tags, [tagName]: value }
				};
			}
		} else {
			formData = { ...formData, [field]: value };
		}
	}

	// Handle switch change
	function handleSwitchChange(checked: boolean) {
		formData = { ...formData, isPublic: checked };
	}

	// Handle form submission
	function handleSubmit() {
		if (!formData.name.trim() || !formData.description.trim()) {
			dispatch('toast', {
				title: "Missing Information",
				description: "Group name and description are required.",
				variant: "destructive",
			});
			return;
		}

		// Create mock group data
		const newGroup = {
			id: `group-${Date.now()}`,
			...formData,
			createdAt: new Date().toISOString(),
			memberCount: 1
		};

		dispatch('toast', {
			title: "Group Created!",
			description: `Your group "${newGroup.name}" has been created. You need to invite 10 members for it to be visible to others.`,
		});
		
		dispatch('toast', {
			title: "Next Goal: Political Organizer",
			description: "Increase your group membership to 1,000 members to earn the Organizer badge!",
			duration: 7000,
		});

		onSubmit?.(newGroup);
		dispatch('submit', newGroup);
		resetForm();
		handleClose();
	}

	// Handle close
	function handleClose() {
		isOpen = false;
		onClose?.();
		dispatch('close');
	}

	// Reset form
	function resetForm() {
		formData = {
			name: '',
			description: '',
			isPublic: true,
			tags: { leader: '', location: '', party: '', keywords: [] }
		};
	}

	// Derived values
	let keywordsString = $derived(formData.tags.keywords.join(', '));
</script>

<Dialog bind:open={isOpen} onOpenChange={handleClose}>
	<div 
		class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg"
		{...restProps}
	>
		<DialogHeader>
			<DialogTitle class="text-primary text-2xl flex items-center">
				<Users size={24} class="mr-2" /> 
				Create a New Group
			</DialogTitle>
			<DialogDescription>
				Build a community around a shared interest, leader, or cause. 
				Groups require 10 members to become visible.
			</DialogDescription>
		</DialogHeader>

		<div class="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-3 text-sm">
			<!-- Group Name -->
			<div>
				<Label for="group-name" class="font-semibold">Group Name</Label>
				<Input 
					id="group-name" 
					value={formData.name} 
					on:input={(e) => handleChange('name', e.target.value)} 
					class="modern-input mt-1" 
				/>
			</div>

			<!-- Description -->
			<div>
				<Label for="group-description" class="font-semibold">Description</Label>
				<Textarea 
					id="group-description" 
					value={formData.description} 
					on:input={(e) => handleChange('description', e.target.value)} 
					class="modern-input min-h-[100px] mt-1" 
				/>
			</div>

			<!-- Privacy Setting -->
			<div class="flex items-center space-x-3 mt-2 rounded-lg p-3 bg-secondary/50">
				<Switch 
					bind:checked={formData.isPublic}
					onCheckedChange={handleSwitchChange}
				/>
				<div>
					<Label class="font-semibold flex items-center">
						{#if formData.isPublic}
							<Globe size={14} class="mr-2 text-green-500" />
							Public Group
						{:else}
							<Lock size={14} class="mr-2 text-red-500" />
							Private Group
						{/if}
					</Label>
					<p class="text-xs text-muted-foreground">
						{formData.isPublic 
							? "Anyone can find and join this group." 
							: "Only invited members can find and join."
						}
					</p>
				</div>
			</div>

			<!-- Tags Section -->
			<div class="space-y-3 mt-2">
				<h4 class="font-semibold">Tags (Optional)</h4>
				
				<div>
					<Label for="tag-leader">Tag Leader</Label>
					<Input 
						id="tag-leader" 
						value={formData.tags.leader} 
						on:input={(e) => handleChange('tag-leader', e.target.value)} 
						class="modern-input mt-1" 
						placeholder="e.g., Peter Obi"
					/>
				</div>

				<div>
					<Label for="tag-location">Tag Location</Label>
					<Input 
						id="tag-location" 
						value={formData.tags.location} 
						on:input={(e) => handleChange('tag-location', e.target.value)} 
						class="modern-input mt-1" 
						placeholder="e.g., Lagos"
					/>
				</div>

				<div>
					<Label for="tag-party">Tag Party</Label>
					<Input 
						id="tag-party" 
						value={formData.tags.party} 
						on:input={(e) => handleChange('tag-party', e.target.value)} 
						class="modern-input mt-1" 
						placeholder="e.g., LP"
					/>
				</div>

				<div>
					<Label for="tag-keywords">Keywords (comma-separated)</Label>
					<Input 
						id="tag-keywords" 
						value={keywordsString} 
						on:input={(e) => handleChange('tag-keywords', e.target.value)} 
						class="modern-input mt-1" 
						placeholder="e.g., economy, security, youths"
					/>
				</div>
			</div>
		</div>

		<DialogFooter class="mt-2">
			<Button type="button" variant="outline" on:click={handleClose}>
				Cancel
			</Button>
			<Button type="button" on:click={handleSubmit} class="btn-primary">
				Create Group
			</Button>
		</DialogFooter>
	</div>
</Dialog>
