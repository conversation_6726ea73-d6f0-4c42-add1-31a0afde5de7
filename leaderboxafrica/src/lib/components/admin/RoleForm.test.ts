import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { roleStore } from '$lib/stores/roles.js';

// Mock the role store
vi.mock('$lib/stores/roles.js', () => ({
	roleStore: {
		state: {
			permissions: [
				{ id: 'p1', name: 'admin:access', resource: 'admin', action: 'access' },
				{ id: 'p2', name: 'users:read', resource: 'users', action: 'read' },
				{ id: 'p3', name: 'users:write', resource: 'users', action: 'write' }
			]
		},
		getPermissionsByCategory: vi.fn(() => ({
			admin: [{ id: 'p1', name: 'admin:access', resource: 'admin', action: 'access' }],
			users: [
				{ id: 'p2', name: 'users:read', resource: 'users', action: 'read' },
				{ id: 'p3', name: 'users:write', resource: 'users', action: 'write' }
			]
		})),
		createRole: vi.fn(),
		updateRole: vi.fn()
	}
}));

// Mock UI components
vi.mock('$lib/components/ui/Button.svelte', () => ({
	default: vi.fn()
}));

vi.mock('$lib/components/ui/Input.svelte', () => ({
	default: vi.fn()
}));

vi.mock('$lib/components/ui/Textarea.svelte', () => ({
	default: vi.fn()
}));

vi.mock('$lib/components/ui/Dialog.svelte', () => ({
	default: vi.fn()
}));

describe('RoleForm Component', () => {
	const mockOnClose = vi.fn();
	const mockOnSave = vi.fn();

	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe('Create Mode', () => {
		it('should create role with valid data', async () => {
			const mockCreatedRole = {
				id: '1',
				name: 'Test Role',
				description: 'Test description',
				isSystem: false,
				permissions: [],
				createdAt: new Date(),
				updatedAt: new Date()
			};

			(roleStore.createRole as any).mockResolvedValueOnce(mockCreatedRole);

			// Test the store method directly
			await roleStore.createRole({
				name: 'Test Role',
				description: 'Test description',
				permissionIds: ['p1', 'p2']
			});

			expect(roleStore.createRole).toHaveBeenCalledWith({
				name: 'Test Role',
				description: 'Test description',
				permissionIds: ['p1', 'p2']
			});
		});
	});

	describe('Edit Mode', () => {
		const existingRole = {
			id: '1',
			name: 'Existing Role',
			description: 'Existing description',
			isSystem: false,
			permissions: [
				{ id: 'p1', name: 'admin:access', resource: 'admin', action: 'access' }
			],
			createdAt: new Date(),
			updatedAt: new Date()
		};

		it('should update role with valid data', async () => {
			const mockUpdatedRole = {
				...existingRole,
				name: 'Updated Role',
				description: 'Updated description'
			};

			(roleStore.updateRole as any).mockResolvedValueOnce(mockUpdatedRole);

			// Test the store method directly
			await roleStore.updateRole('1', {
				name: 'Updated Role',
				description: 'Updated description',
				permissionIds: ['p1', 'p2']
			});

			expect(roleStore.updateRole).toHaveBeenCalledWith('1', {
				name: 'Updated Role',
				description: 'Updated description',
				permissionIds: ['p1', 'p2']
			});
		});
	});

	describe('Permission Management', () => {
		it('should handle permission selection', () => {
			// Test permission category grouping
			const categories = roleStore.getPermissionsByCategory();
			expect(categories).toEqual({
				admin: [{ id: 'p1', name: 'admin:access', resource: 'admin', action: 'access' }],
				users: [
					{ id: 'p2', name: 'users:read', resource: 'users', action: 'read' },
					{ id: 'p3', name: 'users:write', resource: 'users', action: 'write' }
				]
			});
		});
	});

	describe('Form Validation', () => {
		it('should validate role name length', () => {
			// Test minimum length validation
			const shortName = 'A';
			expect(shortName.length).toBeLessThan(2);
		});

		it('should validate required fields', () => {
			const emptyName = '';
			expect(emptyName.trim()).toBe('');
		});
	});

	describe('Error Handling', () => {
		it('should handle create role error', async () => {
			(roleStore.createRole as any).mockResolvedValueOnce(null);
			roleStore.state.error = 'Role name already exists';

			await roleStore.createRole({
				name: 'Duplicate Role',
				permissionIds: []
			});

			expect(roleStore.createRole).toHaveBeenCalled();
			expect(roleStore.state.error).toBe('Role name already exists');
		});

		it('should handle update role error', async () => {
			(roleStore.updateRole as any).mockResolvedValueOnce(null);
			roleStore.state.error = 'Cannot modify system role';

			await roleStore.updateRole('1', {
				name: 'System Role'
			});

			expect(roleStore.updateRole).toHaveBeenCalled();
			expect(roleStore.state.error).toBe('Cannot modify system role');
		});
	});
});