<script lang="ts">
	import { roleStore, type CreateRoleData, type UpdateRoleData, type RoleWithPermissions } from '$lib/stores/roles.js';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Label from '$lib/components/ui/Label.svelte';
	import Textarea from '$lib/components/ui/Textarea.svelte';
	import Dialog from '$lib/components/ui/Dialog.svelte';
	import DialogHeader from '$lib/components/ui/DialogHeader.svelte';
	import DialogTitle from '$lib/components/ui/DialogTitle.svelte';
	import DialogFooter from '$lib/components/ui/DialogFooter.svelte';
	import { X } from 'lucide-svelte';

	interface Props {
		open: boolean;
		role?: RoleWithPermissions | null;
		onClose: () => void;
		onSave: (role: RoleWithPermissions) => void;
	}

	let { open = $bindable(), role = null, onClose, onSave }: Props = $props();

	// Form state
	let formData = $state({
		name: '',
		description: '',
		permissionIds: [] as string[]
	});

	let errors = $state({
		name: '',
		general: ''
	});

	let isSubmitting = $state(false);

	// Reactive values
	const isEditing = $derived(role !== null);
	const title = $derived(isEditing ? 'Edit Role' : 'Create Role');
	const submitText = $derived(isEditing ? 'Update Role' : 'Create Role');
	const permissions = $derived(roleStore.state.permissions);
	const permissionsByCategory = $derived(roleStore.getPermissionsByCategory());

	// Initialize form data when role changes
	$effect(() => {
		if (role) {
			formData.name = role.name;
			formData.description = role.description || '';
			formData.permissionIds = role.permissions.map(p => p.id);
		} else {
			formData.name = '';
			formData.description = '';
			formData.permissionIds = [];
		}
		
		// Clear errors when role changes
		errors.name = '';
		errors.general = '';
	});

	function validateForm(): boolean {
		errors.name = '';
		errors.general = '';

		if (!formData.name.trim()) {
			errors.name = 'Role name is required';
			return false;
		}

		if (formData.name.trim().length < 2) {
			errors.name = 'Role name must be at least 2 characters';
			return false;
		}

		return true;
	}

	function togglePermission(permissionId: string) {
		const index = formData.permissionIds.indexOf(permissionId);
		if (index > -1) {
			formData.permissionIds.splice(index, 1);
		} else {
			formData.permissionIds.push(permissionId);
		}
		formData.permissionIds = [...formData.permissionIds]; // Trigger reactivity
	}

	function toggleCategoryPermissions(categoryPermissions: string[]) {
		const allSelected = categoryPermissions.every(id => formData.permissionIds.includes(id));
		
		if (allSelected) {
			// Remove all category permissions
			formData.permissionIds = formData.permissionIds.filter(id => !categoryPermissions.includes(id));
		} else {
			// Add all category permissions
			const newPermissions = categoryPermissions.filter(id => !formData.permissionIds.includes(id));
			formData.permissionIds = [...formData.permissionIds, ...newPermissions];
		}
	}

	function isCategoryFullySelected(categoryPermissions: string[]): boolean {
		return categoryPermissions.every(id => formData.permissionIds.includes(id));
	}

	function isCategoryPartiallySelected(categoryPermissions: string[]): boolean {
		return categoryPermissions.some(id => formData.permissionIds.includes(id)) && 
			   !isCategoryFullySelected(categoryPermissions);
	}

	async function handleSubmit(event: Event) {
		event.preventDefault();
		if (!validateForm()) return;

		isSubmitting = true;
		errors.general = '';

		try {
			let result: RoleWithPermissions | null = null;

			if (isEditing && role) {
				const updateData: UpdateRoleData = {
					name: formData.name.trim(),
					description: formData.description.trim() || undefined,
					permissionIds: formData.permissionIds
				};
				result = await roleStore.updateRole(role.id, updateData);
			} else {
				const createData: CreateRoleData = {
					name: formData.name.trim(),
					description: formData.description.trim() || undefined,
					permissionIds: formData.permissionIds
				};
				result = await roleStore.createRole(createData);
			}

			if (result) {
				onSave(result);
				onClose();
			} else {
				errors.general = roleStore.state.error || 'Failed to save role';
			}
		} catch (error) {
			console.error('Form submission error:', error);
			errors.general = error instanceof Error ? error.message : 'An unexpected error occurred';
		} finally {
			isSubmitting = false;
		}
	}

	function handleClose() {
		if (!isSubmitting) {
			onClose();
		}
	}
</script>

<Dialog bind:open>
	<div class="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
		<div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
			<DialogHeader class="px-6 py-4 border-b flex-shrink-0">
				<div class="flex items-center justify-between">
					<DialogTitle class="text-lg font-semibold">{title}</DialogTitle>
					<Button
						variant="ghost"
						size="sm"
						onclick={handleClose}
						disabled={isSubmitting}
						class="h-8 w-8 p-0"
					>
						<X class="h-4 w-4" />
					</Button>
				</div>
			</DialogHeader>

			<form onsubmit={handleSubmit} class="flex flex-col flex-1 min-h-0">
				<div class="flex-1 overflow-y-auto px-6 py-4 space-y-6 scrollbar-thin">
					<!-- General Error -->
					{#if errors.general}
						<div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
							{errors.general}
						</div>
					{/if}

					<!-- Basic Information -->
					<div class="space-y-4">
						<h3 class="text-sm font-medium text-gray-900">Basic Information</h3>
						
						<div class="space-y-2">
							<Label for="role-name">Role Name *</Label>
							<Input
								id="role-name"
								bind:value={formData.name}
								placeholder="Enter role name"
								disabled={isSubmitting}
								class={errors.name ? 'border-red-500' : ''}
							/>
							{#if errors.name}
								<p class="text-sm text-red-600">{errors.name}</p>
							{/if}
						</div>

						<div class="space-y-2">
							<Label for="role-description">Description</Label>
							<Textarea
								id="role-description"
								bind:value={formData.description}
								placeholder="Enter role description"
								disabled={isSubmitting}
								rows={3}
							/>
						</div>
					</div>

					<!-- Permissions -->
					<div class="space-y-4">
						<h3 class="text-sm font-medium text-gray-900">Permissions</h3>
						
						{#if Object.keys(permissionsByCategory).length === 0}
							<p class="text-sm text-gray-500">Loading permissions...</p>
						{:else}
							<div class="space-y-4">
								{#each Object.entries(permissionsByCategory) as [category, categoryPermissions]}
									{@const categoryPermissionIds = categoryPermissions.map(p => p.id)}
									{@const isFullySelected = isCategoryFullySelected(categoryPermissionIds)}
									{@const isPartiallySelected = isCategoryPartiallySelected(categoryPermissionIds)}
									
									<div class="border rounded-lg p-4">
										<div class="flex items-center space-x-2 mb-3">
											<input
												type="checkbox"
												id="category-{category}"
												checked={isFullySelected}
												indeterminate={isPartiallySelected}
												onchange={() => toggleCategoryPermissions(categoryPermissionIds)}
												disabled={isSubmitting}
												class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
											/>
											<label 
												for="category-{category}"
												class="text-sm font-medium text-gray-900 capitalize cursor-pointer"
											>
												{category}
											</label>
										</div>
										
										<div class="grid grid-cols-1 sm:grid-cols-2 gap-2 ml-6">
											{#each categoryPermissions as permission}
												<div class="flex items-center space-x-2">
													<input
														type="checkbox"
														id="permission-{permission.id}"
														checked={formData.permissionIds.includes(permission.id)}
														onchange={() => togglePermission(permission.id)}
														disabled={isSubmitting}
														class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
													/>
													<label 
														for="permission-{permission.id}"
														class="text-sm text-gray-700 cursor-pointer"
													>
														{permission.name}
													</label>
												</div>
											{/each}
										</div>
									</div>
								{/each}
							</div>
						{/if}
					</div>
				</div>

				<DialogFooter class="px-6 py-4 border-t bg-gray-50 flex-shrink-0">
					<div class="flex justify-end space-x-2">
						<Button
							type="button"
							variant="outline"
							onclick={handleClose}
							disabled={isSubmitting}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							disabled={isSubmitting}
							class="min-w-[100px]"
						>
							{#if isSubmitting}
								<div class="flex items-center space-x-2">
									<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
									<span>Saving...</span>
								</div>
							{:else}
								{submitText}
							{/if}
						</Button>
					</div>
				</DialogFooter>
			</form>
		</div>
	</div>
</Dialog>