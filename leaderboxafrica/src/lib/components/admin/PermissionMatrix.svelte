<script lang="ts">
	import { roleStore, type RoleWithPermissions } from '$lib/stores/roles.js';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import CardTitle from '$lib/components/ui/CardTitle.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import Badge from '$lib/components/ui/Badge.svelte';
	import { Check, X, Save, RotateCcw } from 'lucide-svelte';
	import type { Permission } from '@prisma/client';

	interface Props {
		selectedRole: RoleWithPermissions;
		permissions: Permission[];
		onSave?: (roleId: string, permissionIds: string[]) => Promise<void>;
	}

	let { selectedRole, permissions, onSave }: Props = $props();

	// Component state - using proper reactive patterns
	let selectedPermissions = $state<Set<string>>(new Set());
	let originalPermissions = $state<Set<string>>(new Set());
	let isSaving = $state(false);

	// Derived values - computed from state, no side effects
	const permissionsByCategory = $derived.by(() => {
		const categories: Record<string, Permission[]> = {};
		permissions.forEach(permission => {
			const category = permission.resource;
			if (!categories[category]) {
				categories[category] = [];
			}
			categories[category].push(permission);
		});
		return categories;
	});

	// Derived state for change tracking - no side effects
	const hasChanges = $derived(
		selectedPermissions.size !== originalPermissions.size ||
		!areSetsEqual(selectedPermissions, originalPermissions)
	);

	// Initialize permissions when role changes - single effect with proper dependency tracking
	$effect(() => {
		if (selectedRole?.id) {
			const rolePermissionIds = new Set(selectedRole.permissions.map(p => p.id));
			selectedPermissions = new Set(rolePermissionIds);
			originalPermissions = new Set(rolePermissionIds);
		}
	});

	function areSetsEqual(set1: Set<string>, set2: Set<string>): boolean {
		if (set1.size !== set2.size) return false;
		for (const item of set1) {
			if (!set2.has(item)) return false;
		}
		return true;
	}

	// Optimized toggle functions - industry standard approach
	function togglePermission(permissionId: string) {
		const newPermissions = new Set(selectedPermissions);
		if (newPermissions.has(permissionId)) {
			newPermissions.delete(permissionId);
		} else {
			newPermissions.add(permissionId);
		}
		selectedPermissions = newPermissions;
	}

	function toggleCategoryPermissions(categoryPermissions: Permission[]) {
		const categoryPermissionIds = categoryPermissions.map(p => p.id);
		const allSelected = categoryPermissionIds.every(id => selectedPermissions.has(id));
		
		const newPermissions = new Set(selectedPermissions);
		
		if (allSelected) {
			// Remove all category permissions
			categoryPermissionIds.forEach(id => newPermissions.delete(id));
		} else {
			// Add all category permissions
			categoryPermissionIds.forEach(id => newPermissions.add(id));
		}
		
		selectedPermissions = newPermissions;
	}

	// Optimized category state functions - single pass through permissions
	function getCategorySelectionState(categoryPermissions: Permission[]): 'none' | 'partial' | 'full' {
		const selectedCount = categoryPermissions.reduce((count, p) => 
			selectedPermissions.has(p.id) ? count + 1 : count, 0
		);
		
		if (selectedCount === 0) return 'none';
		if (selectedCount === categoryPermissions.length) return 'full';
		return 'partial';
	}

	// Helper functions for backward compatibility
	function isCategoryFullySelected(categoryPermissions: Permission[]): boolean {
		return getCategorySelectionState(categoryPermissions) === 'full';
	}

	function isCategoryPartiallySelected(categoryPermissions: Permission[]): boolean {
		return getCategorySelectionState(categoryPermissions) === 'partial';
	}

	// Optimized save function with proper error handling
	async function handleSave() {
		if (!hasChanges || isSaving) return;

		isSaving = true;
		try {
			const permissionIds = Array.from(selectedPermissions);
			
			if (onSave) {
				await onSave(selectedRole.id, permissionIds);
			} else {
				await roleStore.updateRole(selectedRole.id, { permissionIds });
			}
			
			// Update original permissions to reflect saved state
			originalPermissions = new Set(selectedPermissions);
		} catch (error) {
			console.error('Failed to save permissions:', error);
			// Optionally show user-friendly error message
		} finally {
			isSaving = false;
		}
	}

	// Optimized bulk action functions
	function handleReset() {
		selectedPermissions = new Set(originalPermissions);
	}

	function selectAllPermissions() {
		selectedPermissions = new Set(permissions.map(p => p.id));
	}

	function clearAllPermissions() {
		selectedPermissions = new Set();
	}

	function getPermissionDescription(permission: Permission): string {
		return permission.description || `${permission.action} access to ${permission.resource}`;
	}
</script>

<Card class="h-full">
	<CardHeader class="pb-4">
		<div class="flex items-center justify-between">
			<div>
				<CardTitle class="text-lg">Permission Matrix</CardTitle>
				<p class="text-sm text-gray-600 mt-1">
					Manage permissions for <strong>{selectedRole.name}</strong>
				</p>
			</div>
			
			<div class="flex items-center space-x-2">
				<Badge variant="outline" class="text-xs">
					{selectedPermissions.size} of {permissions.length} selected
				</Badge>
				
				{#if hasChanges}
					<Badge variant="secondary" class="text-xs">
						Unsaved changes
					</Badge>
				{/if}
			</div>
		</div>
	</CardHeader>

	<CardContent class="space-y-6">
		<!-- Bulk Actions -->
		<div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
			<div class="flex items-center space-x-2">
				<span class="text-sm font-medium text-gray-700">Bulk Actions:</span>
				<Button
					variant="outline"
					size="sm"
					onclick={selectAllPermissions}
					disabled={isSaving}
				>
					Select All
				</Button>
				<Button
					variant="outline"
					size="sm"
					onclick={clearAllPermissions}
					disabled={isSaving}
				>
					Clear All
				</Button>
			</div>
			
			<div class="flex items-center space-x-2">
				{#if hasChanges}
					<Button
						variant="outline"
						size="sm"
						onclick={handleReset}
						disabled={isSaving}
					>
						<RotateCcw class="h-4 w-4 mr-1" />
						Reset
					</Button>
				{/if}
				
				<Button
					onclick={handleSave}
					disabled={!hasChanges || isSaving}
					size="sm"
				>
					{#if isSaving}
						<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
					{:else}
						<Save class="h-4 w-4 mr-1" />
					{/if}
					Save Changes
				</Button>
			</div>
		</div>

		<!-- Permission Categories -->
		<div class="space-y-4 max-h-96 overflow-y-auto">
			{#each Object.entries(permissionsByCategory) as [category, categoryPermissions]}
				{@const selectionState = getCategorySelectionState(categoryPermissions)}
				
				<div class="border rounded-lg">
					<!-- Category Header -->
					<div class="p-4 border-b bg-gray-50">
						<div class="flex items-center justify-between">
							<div class="flex items-center space-x-3">
								<button
									onclick={() => toggleCategoryPermissions(categoryPermissions)}
									disabled={isSaving}
									class="flex items-center space-x-2 hover:bg-gray-100 p-1 rounded transition-colors"
								>
									<div class="relative">
										{#if selectionState === 'full'}
											<div class="h-5 w-5 bg-blue-600 border-2 border-blue-600 rounded flex items-center justify-center">
												<Check class="h-3 w-3 text-white" />
											</div>
										{:else if selectionState === 'partial'}
											<div class="h-5 w-5 bg-blue-100 border-2 border-blue-600 rounded flex items-center justify-center">
												<div class="h-2 w-2 bg-blue-600 rounded"></div>
											</div>
										{:else}
											<div class="h-5 w-5 border-2 border-gray-300 rounded"></div>
										{/if}
									</div>
									
									<h3 class="text-sm font-medium text-gray-900 capitalize">
										{category}
									</h3>
								</button>
								
								<Badge variant="outline" class="text-xs">
									{categoryPermissions.filter(p => selectedPermissions.has(p.id)).length} / {categoryPermissions.length}
								</Badge>
							</div>
							
							<div class="text-xs text-gray-500">
								{selectionState === 'full' ? 'All selected' : 
								 selectionState === 'partial' ? 'Partially selected' : 
								 'None selected'}
							</div>
						</div>
					</div>
					
					<!-- Category Permissions -->
					<div class="p-4">
						<div class="grid grid-cols-1 gap-3">
							{#each categoryPermissions as permission}
								{@const isSelected = selectedPermissions.has(permission.id)}
								
								<div class="flex items-start space-x-3 p-2 rounded hover:bg-gray-50 transition-colors">
									<button
										onclick={() => togglePermission(permission.id)}
										disabled={isSaving}
										class="mt-0.5"
									>
										<div class="relative">
											{#if isSelected}
												<div class="h-4 w-4 bg-blue-600 border-2 border-blue-600 rounded flex items-center justify-center">
													<Check class="h-2.5 w-2.5 text-white" />
												</div>
											{:else}
												<div class="h-4 w-4 border-2 border-gray-300 rounded hover:border-blue-400 transition-colors"></div>
											{/if}
										</div>
									</button>
									
									<div class="flex-1 min-w-0">
										<div class="flex items-center justify-between">
											<label 
												for="permission-{permission.id}"
												class="text-sm font-medium text-gray-900 cursor-pointer"
											>
												{permission.name}
											</label>
											
											<Badge 
												variant={isSelected ? 'default' : 'outline'} 
												class="text-xs ml-2"
											>
												{permission.action}
											</Badge>
										</div>
										
										<p class="text-xs text-gray-600 mt-1">
											{getPermissionDescription(permission)}
										</p>
									</div>
								</div>
							{/each}
						</div>
					</div>
				</div>
			{/each}
		</div>

		<!-- Summary -->
		<div class="p-3 bg-blue-50 rounded-lg">
			<div class="flex items-center justify-between text-sm">
				<span class="text-blue-800">
					<strong>{selectedPermissions.size}</strong> permissions selected
				</span>
				
				{#if hasChanges}
					<span class="text-blue-600">
						{selectedPermissions.size - originalPermissions.size > 0 ? '+' : ''}{selectedPermissions.size - originalPermissions.size} changes
					</span>
				{/if}
			</div>
		</div>
	</CardContent>
</Card>