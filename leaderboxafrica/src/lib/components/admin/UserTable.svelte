<!-- User Table Component - Svelte 5 conversion of React UserTable.jsx -->
<script lang="ts">
	import Button from '$lib/components/ui/Button.svelte';
	import Avatar from '$lib/components/ui/Avatar.svelte';
	import Badge from '$lib/components/ui/Badge.svelte';
	import { Edit, Trash2, Flag, UserX, ChevronDown, ChevronUp } from 'lucide-svelte';
	import { createEventDispatcher } from 'svelte';

	// Props
	let { 
		users = [],
		sortConfig = { key: '', direction: 'ascending' },
		onEdit,
		onFlag,
		onBan,
		onUnban,
		onDelete,
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// Helper function to get sort icon
	function getSortIcon(key: string) {
		if (sortConfig.key !== key) {
			return { component: ChevronDown, class: "opacity-0 group-hover:opacity-50" };
		}
		return sortConfig.direction === 'ascending' 
			? { component: ChevronUp, class: "" }
			: { component: ChevronDown, class: "" };
	}

	// Event handlers
	function handleSort(key: string) {
		dispatch('sort', key);
	}

	function handleEdit(user: any) {
		onEdit?.(user);
		dispatch('edit', user);
	}

	function handleFlag(userId: string, userName: string) {
		onFlag?.(userId, userName);
		dispatch('flag', { userId, userName });
	}

	function handleBan(userId: string, userName: string) {
		onBan?.(userId, userName);
		dispatch('ban', { userId, userName });
	}

	function handleUnban(userId: string, userName: string) {
		onUnban?.(userId, userName);
		dispatch('unban', { userId, userName });
	}

	function handleDelete(userId: string, userName: string) {
		onDelete?.(userId, userName);
		dispatch('delete', { userId, userName });
	}

	// Helper functions
	function getUserDisplayName(user: any) {
		return user.name || 'N/A';
	}

	function getUserAvatarFallback(user: any) {
		return user.name 
			? user.name.substring(0, 1).toUpperCase() 
			: user.email.substring(0, 1).toUpperCase();
	}

	function getStatusBadgeVariant(status: string) {
		switch (status) {
			case 'active': return 'default';
			case 'banned': return 'destructive';
			case 'pending': return 'secondary';
			default: return 'outline';
		}
	}

	function getRoleBadgeVariant(role: string) {
		switch (role) {
			case 'admin': return 'default';
			case 'leader': return 'secondary';
			case 'user': return 'outline';
			default: return 'outline';
		}
	}
</script>

{#if users.length === 0}
	<p class="text-muted-foreground text-center py-10">No users match your criteria.</p>
{:else}
	<div class="overflow-x-auto" {...restProps}>
		<table class="min-w-full divide-y divide-border text-sm">
			<!-- Table Header -->
			<thead class="bg-secondary/50">
				<tr>
					<th 
						scope="col" 
						class="px-3 py-2.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider group cursor-pointer" 
						on:click={() => handleSort('name')}
						role="button"
						tabindex="0"
						on:keydown={(e) => e.key === 'Enter' && handleSort('name')}
					>
						<div class="flex items-center gap-1">
							Name 
							{@const iconInfo = getSortIcon('name')}
							<svelte:component this={iconInfo.component} size={14} class={iconInfo.class} />
						</div>
					</th>
					<th 
						scope="col" 
						class="px-3 py-2.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider group cursor-pointer" 
						on:click={() => handleSort('email')}
						role="button"
						tabindex="0"
						on:keydown={(e) => e.key === 'Enter' && handleSort('email')}
					>
						<div class="flex items-center gap-1">
							Email 
							{@const iconInfo = getSortIcon('email')}
							<svelte:component this={iconInfo.component} size={14} class={iconInfo.class} />
						</div>
					</th>
					<th 
						scope="col" 
						class="px-3 py-2.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider group cursor-pointer" 
						on:click={() => handleSort('role')}
						role="button"
						tabindex="0"
						on:keydown={(e) => e.key === 'Enter' && handleSort('role')}
					>
						<div class="flex items-center gap-1">
							Role 
							{@const iconInfo = getSortIcon('role')}
							<svelte:component this={iconInfo.component} size={14} class={iconInfo.class} />
						</div>
					</th>
					<th 
						scope="col" 
						class="px-3 py-2.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider group cursor-pointer" 
						on:click={() => handleSort('status')}
						role="button"
						tabindex="0"
						on:keydown={(e) => e.key === 'Enter' && handleSort('status')}
					>
						<div class="flex items-center gap-1">
							Status 
							{@const iconInfo = getSortIcon('status')}
							<svelte:component this={iconInfo.component} size={14} class={iconInfo.class} />
						</div>
					</th>
					<th scope="col" class="px-3 py-2.5 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
						Actions
					</th>
				</tr>
			</thead>

			<!-- Table Body -->
			<tbody class="bg-card divide-y divide-border">
				{#each users as user (user.id)}
					<tr class="hover:bg-secondary/30 transition-colors">
						<!-- Name Column -->
						<td class="px-3 py-2.5 whitespace-nowrap">
							<div class="flex items-center">
								<Avatar 
									src={user.avatarUrl || `https://avatar.vercel.sh/${user.name || user.email}.png?size=28`}
									alt={user.name}
									fallback={getUserAvatarFallback(user)}
									className="h-7 w-7 mr-2"
								/>
								<span class="font-medium text-foreground">{getUserDisplayName(user)}</span>
							</div>
						</td>

						<!-- Email Column -->
						<td class="px-3 py-2.5 whitespace-nowrap text-muted-foreground">
							{user.email}
						</td>

						<!-- Role Column -->
						<td class="px-3 py-2.5 whitespace-nowrap">
							<Badge variant={getRoleBadgeVariant(user.role)} class="capitalize">
								{user.role}
							</Badge>
						</td>

						<!-- Status Column -->
						<td class="px-3 py-2.5 whitespace-nowrap">
							<Badge variant={getStatusBadgeVariant(user.status)} class="capitalize">
								{user.status}
							</Badge>
						</td>

						<!-- Actions Column -->
						<td class="px-3 py-2.5 whitespace-nowrap">
							<div class="flex items-center gap-1">
								<!-- Edit Button -->
								<Button 
									variant="ghost" 
									size="icon" 
									on:click={() => handleEdit(user)} 
									class="text-primary hover:text-primary/80 h-7 w-7"
								>
									<Edit size={14} />
								</Button>

								<!-- Ban/Unban and Flag Buttons -->
								{#if user.status !== 'banned'}
									<!-- Flag Button -->
									<Button 
										variant="ghost" 
										size="icon" 
										on:click={() => handleFlag(user.id, user.name || user.email)} 
										class="text-yellow-500 hover:text-yellow-600 h-7 w-7"
									>
										<Flag size={14} />
									</Button>
									<!-- Ban Button -->
									<Button 
										variant="ghost" 
										size="icon" 
										on:click={() => handleBan(user.id, user.name || user.email)} 
										class="text-red-600 hover:text-red-700 h-7 w-7"
									>
										<UserX size={14} />
									</Button>
								{:else}
									<!-- Unban Button -->
									<Button 
										variant="ghost" 
										size="icon" 
										on:click={() => handleUnban(user.id, user.name || user.email)} 
										class="text-green-500 hover:text-green-600 h-7 w-7"
									>
										<UserX size={14} />
									</Button>
								{/if}

								<!-- Delete Button -->
								<Button 
									variant="ghost" 
									size="icon" 
									on:click={() => handleDelete(user.id, user.name || user.email)} 
									class="text-destructive hover:text-destructive/80 h-7 w-7"
								>
									<Trash2 size={14} />
								</Button>
							</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>
{/if}
