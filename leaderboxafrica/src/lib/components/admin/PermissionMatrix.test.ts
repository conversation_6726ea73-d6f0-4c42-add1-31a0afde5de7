import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { roleStore } from '$lib/stores/roles.js';
import type { Permission } from '@prisma/client';

// Mock the role store
vi.mock('$lib/stores/roles.js', () => ({
	roleStore: {
		updateRole: vi.fn()
	}
}));

// Mock UI components
vi.mock('$lib/components/ui/Button.svelte', () => ({
	default: vi.fn()
}));

vi.mock('$lib/components/ui/Card.svelte', () => ({
	default: vi.fn()
}));

describe('PermissionMatrix Component', () => {
	const mockRole = {
		id: '1',
		name: 'Test Role',
		description: 'Test description',
		isSystem: false,
		permissions: [
			{ id: 'p1', name: 'admin:access', resource: 'admin', action: 'access' }
		],
		createdAt: new Date(),
		updatedAt: new Date()
	};

	const mockPermissions: Permission[] = [
		{ id: 'p1', name: 'admin:access', resource: 'admin', action: 'access', scope: null, description: 'Access admin dashboard', createdAt: new Date() },
		{ id: 'p2', name: 'users:read', resource: 'users', action: 'read', scope: null, description: 'View user profiles', createdAt: new Date() },
		{ id: 'p3', name: 'users:write', resource: 'users', action: 'write', scope: null, description: 'Edit user profiles', createdAt: new Date() },
		{ id: 'p4', name: 'content:moderate', resource: 'content', action: 'moderate', scope: null, description: 'Moderate content', createdAt: new Date() }
	];

	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	describe('Permission Grouping', () => {
		it('should group permissions by category', () => {
			const categories: Record<string, Permission[]> = {};
			mockPermissions.forEach(permission => {
				const category = permission.resource;
				if (!categories[category]) {
					categories[category] = [];
				}
				categories[category].push(permission);
			});

			expect(categories).toEqual({
				admin: [mockPermissions[0]],
				users: [mockPermissions[1], mockPermissions[2]],
				content: [mockPermissions[3]]
			});
		});
	});

	describe('Permission Selection Logic', () => {
		it('should handle individual permission toggle', () => {
			const selectedPermissions = new Set(['p1']);
			const permissionId = 'p2';

			// Toggle on
			if (selectedPermissions.has(permissionId)) {
				selectedPermissions.delete(permissionId);
			} else {
				selectedPermissions.add(permissionId);
			}

			expect(selectedPermissions.has('p2')).toBe(true);
			expect(selectedPermissions.size).toBe(2);
		});

		it('should handle category permission toggle', () => {
			const selectedPermissions = new Set(['p1']);
			const categoryPermissions = [mockPermissions[1], mockPermissions[2]]; // users permissions
			const categoryPermissionIds = categoryPermissions.map(p => p.id);
			const allSelected = categoryPermissionIds.every(id => selectedPermissions.has(id));

			if (allSelected) {
				// Remove all category permissions
				categoryPermissionIds.forEach(id => selectedPermissions.delete(id));
			} else {
				// Add all category permissions
				categoryPermissionIds.forEach(id => selectedPermissions.add(id));
			}

			expect(selectedPermissions.has('p2')).toBe(true);
			expect(selectedPermissions.has('p3')).toBe(true);
			expect(selectedPermissions.size).toBe(3);
		});

		it('should detect category selection states', () => {
			const selectedPermissions = new Set(['p2']); // Only one users permission
			const categoryPermissions = [mockPermissions[1], mockPermissions[2]]; // users permissions

			const selectedCount = categoryPermissions.filter(p => selectedPermissions.has(p.id)).length;
			let selectionState: 'none' | 'partial' | 'full';
			
			if (selectedCount === 0) {
				selectionState = 'none';
			} else if (selectedCount === categoryPermissions.length) {
				selectionState = 'full';
			} else {
				selectionState = 'partial';
			}

			expect(selectionState).toBe('partial');
		});

		it('should detect full category selection', () => {
			const selectedPermissions = new Set(['p2', 'p3']); // All users permissions
			const categoryPermissions = [mockPermissions[1], mockPermissions[2]]; // users permissions

			const allSelected = categoryPermissions.every(p => selectedPermissions.has(p.id));
			expect(allSelected).toBe(true);
		});

		it('should detect no category selection', () => {
			const selectedPermissions = new Set(['p1']); // Only admin permission
			const categoryPermissions = [mockPermissions[1], mockPermissions[2]]; // users permissions

			const noneSelected = categoryPermissions.every(p => !selectedPermissions.has(p.id));
			expect(noneSelected).toBe(true);
		});
	});

	describe('Bulk Operations', () => {
		it('should select all permissions', () => {
			const selectedPermissions = new Set<string>();
			
			// Select all
			mockPermissions.forEach(p => selectedPermissions.add(p.id));

			expect(selectedPermissions.size).toBe(mockPermissions.length);
			expect(Array.from(selectedPermissions)).toEqual(['p1', 'p2', 'p3', 'p4']);
		});

		it('should clear all permissions', () => {
			const selectedPermissions = new Set(['p1', 'p2', 'p3', 'p4']);
			
			// Clear all
			selectedPermissions.clear();

			expect(selectedPermissions.size).toBe(0);
		});

		it('should reset to original permissions', () => {
			const originalPermissions = new Set(['p1']);
			const selectedPermissions = new Set(['p1', 'p2', 'p3']);
			
			// Reset
			const resetPermissions = new Set(originalPermissions);

			expect(resetPermissions.size).toBe(1);
			expect(resetPermissions.has('p1')).toBe(true);
			expect(resetPermissions.has('p2')).toBe(false);
		});
	});

	describe('Change Detection', () => {
		it('should detect changes from original state', () => {
			const originalPermissions = new Set(['p1']);
			const selectedPermissions = new Set(['p1', 'p2']);

			function areSetsEqual(set1: Set<string>, set2: Set<string>): boolean {
				if (set1.size !== set2.size) return false;
				for (const item of set1) {
					if (!set2.has(item)) return false;
				}
				return true;
			}

			const hasChanges = !areSetsEqual(selectedPermissions, originalPermissions);
			expect(hasChanges).toBe(true);
		});

		it('should detect no changes when sets are equal', () => {
			const originalPermissions = new Set(['p1', 'p2']);
			const selectedPermissions = new Set(['p1', 'p2']);

			function areSetsEqual(set1: Set<string>, set2: Set<string>): boolean {
				if (set1.size !== set2.size) return false;
				for (const item of set1) {
					if (!set2.has(item)) return false;
				}
				return true;
			}

			const hasChanges = !areSetsEqual(selectedPermissions, originalPermissions);
			expect(hasChanges).toBe(false);
		});
	});

	describe('Save Functionality', () => {
		it('should call onSave callback when provided', async () => {
			const mockOnSave = vi.fn().mockResolvedValueOnce(undefined);
			const selectedPermissions = new Set(['p1', 'p2']);

			await mockOnSave(mockRole.id, Array.from(selectedPermissions));

			expect(mockOnSave).toHaveBeenCalledWith('1', ['p1', 'p2']);
		});

		it('should call roleStore.updateRole when no onSave callback', async () => {
			const selectedPermissions = new Set(['p1', 'p2']);
			(roleStore.updateRole as any).mockResolvedValueOnce({});

			await roleStore.updateRole(mockRole.id, { 
				permissionIds: Array.from(selectedPermissions) 
			});

			expect(roleStore.updateRole).toHaveBeenCalledWith('1', {
				permissionIds: ['p1', 'p2']
			});
		});
	});

	describe('Permission Description', () => {
		it('should use permission description when available', () => {
			const permission = mockPermissions[0];
			const description = permission.description || `${permission.action} access to ${permission.resource}`;
			
			expect(description).toBe('Access admin dashboard');
		});

		it('should generate description when not available', () => {
			const permission = { ...mockPermissions[0], description: null };
			const description = permission.description || `${permission.action} access to ${permission.resource}`;
			
			expect(description).toBe('access access to admin');
		});
	});
});