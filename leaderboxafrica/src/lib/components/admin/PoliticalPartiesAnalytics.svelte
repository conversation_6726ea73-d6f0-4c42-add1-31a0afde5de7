<!-- Political Parties Analytics Dashboard Component -->
<script lang="ts">
	import { onMount } from 'svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import CardTitle from '$lib/components/ui/CardTitle.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import { toast } from 'svelte-sonner';

	// State management using Svelte 5 runes
	let analytics = $state({
		totalParties: 0,
		activeParties: 0,
		inactiveParties: 0,
		activeRate: 0,
		totalLeaders: 0,
		totalFollowers: 0,
		averageRating: 0,
		dominantParty: null,
		topParties: [],
		partyEngagement: [],
		ideologyDistribution: [],
		ageGroups: {},
		stateDistribution: []
	});
	
	let loading = $state(false);
	let lastUpdated = $state(null);

	// Load analytics data
	async function loadAnalytics() {
		loading = true;
		try {
			const response = await fetch('/api/admin/political-parties/analytics');
			const data = await response.json();

			if (data.success) {
				analytics = data.data;
				lastUpdated = new Date();
				toast.success('Analytics updated successfully');
			} else {
				toast.error('Failed to load analytics');
			}
		} catch (error) {
			console.error('Error loading analytics:', error);
			toast.error('Failed to load analytics');
		} finally {
			loading = false;
		}
	}

	// Format number with commas
	function formatNumber(num) {
		return num?.toLocaleString() || '0';
	}

	// Format percentage
	function formatPercentage(num) {
		return `${(num || 0).toFixed(1)}%`;
	}

	// Get status color
	function getStatusColor(isActive) {
		return isActive ? 'text-green-600' : 'text-red-600';
	}

	// Get engagement level color
	function getEngagementColor(followers) {
		if (followers > 2000) return 'text-green-600';
		if (followers > 1000) return 'text-yellow-600';
		return 'text-red-600';
	}

	// Load analytics on mount
	onMount(() => {
		loadAnalytics();
	});
</script>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex justify-between items-center">
		<div>
			<h2 class="text-2xl font-bold text-gray-900">Political Parties Analytics</h2>
			<p class="text-gray-600 mt-1">Comprehensive insights into party representation and engagement</p>
		</div>
		<div class="flex items-center space-x-3">
			{#if lastUpdated}
				<span class="text-sm text-gray-500">
					Last updated: {lastUpdated.toLocaleTimeString()}
				</span>
			{/if}
			<Button onclick={loadAnalytics} disabled={loading} size="sm">
				{#if loading}
					<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
					Loading...
				{:else}
					<svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
					</svg>
					Refresh
				{/if}
			</Button>
		</div>
	</div>

	{#if loading && !lastUpdated}
		<div class="flex items-center justify-center py-12">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
			<span class="ml-3 text-gray-600">Loading analytics...</span>
		</div>
	{:else}
		<!-- Key Metrics Cards -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			<Card>
				<CardHeader className="pb-2">
					<CardTitle className="text-sm font-medium text-gray-600">Total Parties</CardTitle>
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold text-gray-900">{formatNumber(analytics.totalParties)}</div>
					<div class="flex items-center mt-1">
						<span class="text-green-600 text-sm">🟢 {analytics.activeParties} active</span>
						<span class="text-red-600 text-sm ml-2">🔴 {analytics.inactiveParties} inactive</span>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader className="pb-2">
					<CardTitle className="text-sm font-medium text-gray-600">Active Rate</CardTitle>
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold text-gray-900">{formatPercentage(analytics.activeRate)}</div>
					<div class="text-sm text-gray-600 mt-1">
						{analytics.activeParties} of {analytics.totalParties} parties
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader className="pb-2">
					<CardTitle className="text-sm font-medium text-gray-600">Total Leaders</CardTitle>
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold text-gray-900">{formatNumber(analytics.totalLeaders)}</div>
					<div class="text-sm text-gray-600 mt-1">
						Across all parties
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader className="pb-2">
					<CardTitle className="text-sm font-medium text-gray-600">Total Followers</CardTitle>
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold text-gray-900">{formatNumber(analytics.totalFollowers)}</div>
					<div class="text-sm text-gray-600 mt-1">
						⭐ Avg rating: {analytics.averageRating?.toFixed(2) || 'N/A'}
					</div>
				</CardContent>
			</Card>
		</div>

		<!-- Top Parties and Engagement -->
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<!-- Top Parties by Representation -->
			<Card>
				<CardHeader>
					<CardTitle>Top Parties by Leader Count</CardTitle>
				</CardHeader>
				<CardContent>
					{#if analytics.topParties?.length > 0}
						<div class="space-y-4">
							{#each analytics.topParties.slice(0, 5) as party, index}
								<div class="flex items-center justify-between">
									<div class="flex items-center space-x-3">
										<div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-sm font-semibold text-blue-800">
											{index + 1}
										</div>
										<div>
											<div class="font-medium text-gray-900">{party.abbreviation}</div>
											<div class="text-sm text-gray-600">{party.name}</div>
										</div>
									</div>
									<div class="text-right">
										<div class="font-semibold text-gray-900">{party.leaders}</div>
										<div class="text-sm text-gray-600">{formatPercentage(party.percentage)}</div>
									</div>
								</div>
							{/each}
						</div>
					{:else}
						<p class="text-gray-600 text-center py-4">No party data available</p>
					{/if}
				</CardContent>
			</Card>

			<!-- Party Engagement Metrics -->
			<Card>
				<CardHeader>
					<CardTitle>Party Engagement Metrics</CardTitle>
				</CardHeader>
				<CardContent>
					{#if analytics.partyEngagement?.length > 0}
						<div class="space-y-4">
							{#each analytics.partyEngagement.slice(0, 5) as party}
								<div class="border-l-4 border-blue-500 pl-4">
									<div class="flex justify-between items-start">
										<div>
											<div class="font-medium text-gray-900">{party.abbreviation}</div>
											<div class="text-sm text-gray-600">{party.name}</div>
										</div>
										<div class="text-right text-sm">
											<div class={getEngagementColor(party.totalFollowers)}>
												👥 {formatNumber(party.totalFollowers)}
											</div>
											<div class="text-gray-600">
												⭐ {party.avgRating?.toFixed(2) || 'N/A'}
											</div>
										</div>
									</div>
									<div class="mt-2 text-xs text-gray-500">
										📊 {formatNumber(party.totalRatings)} ratings • 
										📈 {formatNumber(party.avgFollowersPerLeader)} avg followers/leader
									</div>
								</div>
							{/each}
						</div>
					{:else}
						<p class="text-gray-600 text-center py-4">No engagement data available</p>
					{/if}
				</CardContent>
			</Card>
		</div>

		<!-- Ideology and Age Distribution -->
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<!-- Ideology Distribution -->
			<Card>
				<CardHeader>
					<CardTitle>Ideological Distribution</CardTitle>
				</CardHeader>
				<CardContent>
					{#if analytics.ideologyDistribution?.length > 0}
						<div class="space-y-3">
							{#each analytics.ideologyDistribution.slice(0, 8) as ideology}
								<div class="flex justify-between items-center">
									<div class="text-sm text-gray-900 flex-1 pr-4">{ideology.name}</div>
									<div class="flex items-center space-x-2">
										<div class="w-16 bg-gray-200 rounded-full h-2">
											<div 
												class="bg-blue-600 h-2 rounded-full" 
												style="width: {(ideology.count / analytics.activeParties * 100)}%"
											></div>
										</div>
										<span class="text-sm font-medium text-gray-900 w-8 text-right">{ideology.count}</span>
									</div>
								</div>
							{/each}
						</div>
					{:else}
						<p class="text-gray-600 text-center py-4">No ideology data available</p>
					{/if}
				</CardContent>
			</Card>

			<!-- Age Distribution -->
			<Card>
				<CardHeader>
					<CardTitle>Party Age Distribution</CardTitle>
				</CardHeader>
				<CardContent>
					{#if analytics.ageGroups && Object.keys(analytics.ageGroups).length > 0}
						<div class="space-y-4">
							{#each Object.entries(analytics.ageGroups) as [group, count]}
								<div class="flex items-center justify-between">
									<div class="text-sm text-gray-900">{group}</div>
									<div class="flex items-center space-x-2">
										<div class="w-20 bg-gray-200 rounded-full h-2">
											<div 
												class="bg-green-600 h-2 rounded-full" 
												style="width: {(count / analytics.activeParties * 100)}%"
											></div>
										</div>
										<span class="text-sm font-medium text-gray-900 w-8 text-right">{count}</span>
									</div>
								</div>
							{/each}
						</div>
					{:else}
						<p class="text-gray-600 text-center py-4">No age distribution data available</p>
					{/if}
				</CardContent>
			</Card>
		</div>

		<!-- Geographic Distribution -->
		{#if analytics.stateDistribution?.length > 0}
			<Card>
				<CardHeader>
					<CardTitle>Geographic Distribution</CardTitle>
				</CardHeader>
				<CardContent>
					<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
						{#each analytics.stateDistribution.slice(0, 9) as state}
							<div class="border rounded-lg p-4">
								<div class="flex justify-between items-center mb-2">
									<h4 class="font-medium text-gray-900">📍 {state.name}</h4>
									<span class="text-sm font-semibold text-blue-600">{state.totalLeaders} leaders</span>
								</div>
								<div class="space-y-1">
									{#each state.topParties.slice(0, 3) as party}
										<div class="flex justify-between text-sm">
											<span class="text-gray-600">{party.abbreviation}</span>
											<span class="font-medium">{party.count}</span>
										</div>
									{/each}
								</div>
							</div>
						{/each}
					</div>
				</CardContent>
			</Card>
		{/if}

		<!-- Executive Summary -->
		{#if analytics.dominantParty}
			<Card>
				<CardHeader>
					<CardTitle>Executive Summary</CardTitle>
				</CardHeader>
				<CardContent>
					<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
						<div class="text-center">
							<div class="text-2xl font-bold text-blue-600">{analytics.dominantParty.abbreviation}</div>
							<div class="text-sm text-gray-600 mt-1">Most Represented Party</div>
							<div class="text-xs text-gray-500 mt-1">
								{analytics.dominantParty.leaders} leaders ({formatPercentage(analytics.dominantParty.percentage)})
							</div>
						</div>
						<div class="text-center">
							<div class="text-2xl font-bold text-green-600">{analytics.averagePartyAge || 'N/A'}</div>
							<div class="text-sm text-gray-600 mt-1">Average Party Age</div>
							<div class="text-xs text-gray-500 mt-1">Years since founding</div>
						</div>
						<div class="text-center">
							<div class="text-2xl font-bold text-purple-600">{formatNumber(analytics.totalFollowers)}</div>
							<div class="text-sm text-gray-600 mt-1">Total Followers</div>
							<div class="text-xs text-gray-500 mt-1">Across all parties</div>
						</div>
						<div class="text-center">
							<div class="text-2xl font-bold text-yellow-600">{analytics.averageRating?.toFixed(2) || 'N/A'}</div>
							<div class="text-sm text-gray-600 mt-1">Average Rating</div>
							<div class="text-xs text-gray-500 mt-1">Out of 5.0</div>
						</div>
					</div>
				</CardContent>
			</Card>
		{/if}
	{/if}
</div>

<style>
	/* Custom styles for better visual hierarchy */
	:global(.analytics-card) {
		transition: transform 0.2s ease-in-out;
	}
	
	:global(.analytics-card:hover) {
		transform: translateY(-2px);
	}
</style>