<!-- Suggested Action Card - Exact replica of React SuggestedActionCard.jsx -->
<script lang="ts">
	import { goto } from '$app/navigation';
	import Button from '$lib/components/ui/Button.svelte';

	interface Props {
		suggestion: any;
	}

	let { suggestion }: Props = $props();

	function getActionIcon(iconName: string) {
		const icons = {
			'star': { color: 'text-yellow-500', path: 'M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z' },
			'users': { color: 'text-blue-500', path: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z' },
			'bar-chart': { color: 'text-green-500', path: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
			'file-text': { color: 'text-orange-500', path: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' },
			'message-square': { color: 'text-purple-500', path: 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z' }
		};
		return icons[iconName] || icons['star'];
	}

	function handleAction() {
		goto(suggestion.link);
	}

	let iconData = $derived(getActionIcon(suggestion.icon));
</script>

<div class="p-3 border border-border rounded-lg hover:bg-secondary/30 transition-colors">
	<div class="flex items-start space-x-3">
		<div class="flex-shrink-0 pt-1">
			<svg class="w-5 h-5 {iconData.color}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={iconData.path} />
			</svg>
		</div>
		<div class="flex-grow">
			<h4 class="font-semibold text-sm text-foreground">{suggestion.title}</h4>
			<p class="text-xs text-muted-foreground mt-1">{suggestion.description}</p>
			<Button 
				variant="ghost" 
				size="sm" 
				className="mt-2 h-7 px-3 text-xs text-primary hover:bg-primary/10" 
				on:click={handleAction}
			>
				{suggestion.actionText}
			</Button>
		</div>
	</div>
</div>
