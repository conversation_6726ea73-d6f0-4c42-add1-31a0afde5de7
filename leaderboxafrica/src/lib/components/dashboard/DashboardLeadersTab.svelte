<!-- Dashboard Leaders Tab - Exact replica of React DashboardLeadersTab.jsx -->
<script lang="ts">
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import CardHeader from '$lib/components/ui/CardHeader.svelte';
	import Avatar from '$lib/components/ui/Avatar.svelte';

	interface Props {
		leaders?: Array<any>;
	}

	let { leaders = [] }: Props = $props();
</script>

<Card className="modern-card">
	<CardHeader>
		<h3 class="text-xl text-primary">Leaders You Follow ({leaders.length})</h3>
	</CardHeader>
	<CardContent className="space-y-3">
		{#if leaders.length > 0}
			{#each leaders as leader}
				<a 
					href="/leaders/{leader.id}" 
					class="flex items-center space-x-3 p-3 hover:bg-secondary/50 rounded-md transition-colors border border-transparent hover:border-border"
				>
					<Avatar 
						className="h-10 w-10"
						src={leader.avatarUrl}
						alt={leader.name}
						fallback={leader.name.substring(0,1)}
					/>
					<div>
						<p class="font-semibold text-foreground">{leader.name}</p>
						<p class="text-xs text-muted-foreground">{leader.position}</p>
					</div>
				</a>
			{/each}
		{:else}
			<p class="text-muted-foreground p-4 text-center">
				You are not following any leaders yet. 
				<a href="/" class="text-primary hover:underline">Discover leaders now!</a>
			</p>
		{/if}
	</CardContent>
</Card>
