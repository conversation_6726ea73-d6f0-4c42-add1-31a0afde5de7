<!-- Dashboard Feed Tab - Exact replica of React DashboardFeedTab.jsx -->
<script lang="ts">
	import Card from '$lib/components/ui/Card.svelte';
	import CardContent from '$lib/components/ui/CardContent.svelte';
	import FeedItemCard from './FeedItemCard.svelte';

	interface Props {
		items?: Array<any>;
	}

	let { items = [] }: Props = $props();
</script>

<Card className="modern-card">
	<CardContent className="space-y-4 pt-4">
		{#if items.length > 0}
			{#each items as item, index}
				<FeedItemCard {item} key={`${item.type}-${item.data.id}-${index}`} />
			{/each}
		{:else}
			<p class="text-muted-foreground p-4 text-center">
				Your feed is looking a bit empty. Interact more to personalize it or check suggested actions!
			</p>
		{/if}
	</CardContent>
</Card>
