<!-- Suggest Leader Dialog Component - Svelte 5 conversion of React SuggestLeaderDialog.jsx -->
<script lang="ts">
	import Dialog from '$lib/components/ui/Dialog.svelte';
	import DialogHeader from '$lib/components/ui/DialogHeader.svelte';
	import DialogTitle from '$lib/components/ui/DialogTitle.svelte';
	import DialogDescription from '$lib/components/ui/DialogDescription.svelte';
	import DialogFooter from '$lib/components/ui/DialogFooter.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Textarea from '$lib/components/ui/Textarea.svelte';
	import Label from '$lib/components/ui/Label.svelte';
	import { UserPlus } from 'lucide-svelte';
	import { createEventDispatcher } from 'svelte';

	// Props
	let { 
		isOpen = $bindable(false),
		onClose,
		onSubmit,
		leaderName = '',
		...restProps 
	} = $props();

	const dispatch = createEventDispatcher();

	// State
	let formData = $state({
		name: '',
		position: '',
		party: '',
		notes: ''
	});

	// Effects
	$effect(() => {
		if (leaderName && isOpen) {
			formData = { ...formData, name: leaderName };
		}
	});

	// Handle form field changes
	function handleChange(field: string, value: string) {
		formData = { ...formData, [field]: value };
	}

	// Handle form submission
	function handleSubmit(e: Event) {
		e.preventDefault();
		
		if (!formData.name.trim()) {
			dispatch('toast', {
				title: "Error",
				description: "Leader's name is required.",
				variant: "destructive"
			});
			return;
		}
		
		console.log("Leader Suggestion Submitted:", formData);
		
		dispatch('toast', {
			title: "Suggestion Submitted!",
			description: `Thanks for suggesting ${formData.name}. We'll review it shortly.`,
		});

		onSubmit?.(formData);
		dispatch('submit', formData);
		resetForm();
		handleClose();
	}

	// Handle close
	function handleClose() {
		isOpen = false;
		onClose?.();
		dispatch('close');
	}

	// Reset form
	function resetForm() {
		formData = {
			name: '',
			position: '',
			party: '',
			notes: ''
		};
	}
</script>

<Dialog bind:open={isOpen} onOpenChange={handleClose}>
	<div 
		class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg"
		{...restProps}
	>
		<DialogHeader>
			<DialogTitle class="text-primary text-2xl flex items-center">
				<UserPlus size={24} class="mr-2" /> 
				Suggest a New Leader
			</DialogTitle>
			<DialogDescription>
				Help us expand our database. If you know a political leader not listed, please provide their details below.
			</DialogDescription>
		</DialogHeader>

		<form on:submit={handleSubmit} class="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-3 text-sm">
			<!-- Leader's Full Name -->
			<div>
				<Label for="suggest-leader-name" class="text-foreground font-semibold">
					Leader's Full Name
				</Label>
				<Input
					id="suggest-leader-name"
					value={formData.name}
					on:input={(e) => handleChange('name', e.target.value)}
					class="modern-input mt-1"
					required
				/>
			</div>

			<!-- Position -->
			<div>
				<Label for="suggest-leader-position" class="text-foreground font-semibold">
					Position (e.g., Governor, Senator)
				</Label>
				<Input
					id="suggest-leader-position"
					value={formData.position}
					on:input={(e) => handleChange('position', e.target.value)}
					placeholder="Current or former political position"
					class="modern-input mt-1"
				/>
			</div>

			<!-- Political Party -->
			<div>
				<Label for="suggest-leader-party" class="text-foreground font-semibold">
					Political Party
				</Label>
				<Input
					id="suggest-leader-party"
					value={formData.party}
					on:input={(e) => handleChange('party', e.target.value)}
					placeholder="e.g., APC, PDP, LP"
					class="modern-input mt-1"
				/>
			</div>

			<!-- Additional Notes -->
			<div>
				<Label for="suggest-leader-notes" class="text-foreground font-semibold">
					Additional Notes (Optional)
				</Label>
				<Textarea
					id="suggest-leader-notes"
					value={formData.notes}
					on:input={(e) => handleChange('notes', e.target.value)}
					placeholder="Any other relevant information, like state, links to profiles, etc."
					class="modern-input mt-1 min-h-[100px]"
				/>
			</div>
		</form>

		<DialogFooter class="mt-2">
			<Button type="button" variant="outline" on:click={handleClose}>
				Cancel
			</Button>
			<Button type="submit" on:click={handleSubmit} class="btn-primary">
				Submit Suggestion
			</Button>
		</DialogFooter>
	</div>
</Dialog>

<style>
	:global(.modern-input) {
		@apply border-border focus:border-primary focus:ring-primary;
	}
	
	:global(.btn-primary) {
		@apply bg-primary text-primary-foreground hover:bg-primary/90;
	}
</style>
