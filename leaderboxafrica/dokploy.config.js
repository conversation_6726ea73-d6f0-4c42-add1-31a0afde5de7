// Dokploy Configuration for LeaderBox
// This file contains deployment configuration for Dokploy

export default {
  // Application configuration
  app: {
    name: 'leaderbox',
    type: 'nodejs',
    framework: 'sveltekit',
    port: 3000,
    healthCheck: '/health'
  },

  // Build configuration
  build: {
    nodeVersion: '20',
    packageManager: 'pnpm',
    installCommand: 'pnpm install --frozen-lockfile',
    buildCommand: 'pnpm run build',
    startCommand: 'pnpm start',
    outputDirectory: 'build'
  },

  // Environment variables that should be set in Dokploy
  environment: {
    required: [
      'DATABASE_URL',
      'ORIGIN',
      'SESSION_SECRET',
      'ZEPTOMAIL_API_KEY',
      'ZEPTOMAIL_FROM_EMAIL'
    ],
    optional: [
      'REDIS_URL',
      'NODE_ENV',
      'PORT',
      'HOST',
      'MAX_FILE_SIZE',
      'ENABLE_REGISTRATION',
      'ADMIN_EMAIL',
      'APP_URL'
    ]
  },

  // Database configuration
  database: {
    type: 'postgresql',
    name: 'leaderbox',
    migrations: {
      command: 'pnpm run db:migrate:prod',
      seed: 'pnpm run db:seed'
    }
  },

  // Docker configuration (if using Docker deployment)
  docker: {
    dockerfile: 'Dockerfile',
    context: '.',
    buildArgs: {
      NODE_ENV: 'production'
    }
  },

  // Health check configuration
  healthCheck: {
    path: '/health',
    interval: 30,
    timeout: 10,
    retries: 3
  },

  // Resource requirements
  resources: {
    memory: '512Mi',
    cpu: '0.5',
    replicas: 1
  },

  // Scaling configuration
  scaling: {
    minReplicas: 1,
    maxReplicas: 3,
    targetCPU: 70,
    targetMemory: 80
  },

  // Volume mounts for persistent storage
  volumes: [
    {
      name: 'uploads',
      mountPath: '/app/uploads',
      size: '1Gi'
    }
  ],

  // Network configuration
  network: {
    domains: [
      // Add your domains here
      // 'leaderbox.com',
      // 'www.leaderbox.com'
    ],
    ssl: true,
    redirectHTTP: true
  },

  // Backup configuration
  backup: {
    enabled: true,
    schedule: '0 2 * * *', // Daily at 2 AM
    retention: '30d',
    targets: ['database', 'uploads']
  },

  // Monitoring and logging
  monitoring: {
    enabled: true,
    metrics: true,
    logs: {
      level: 'info',
      retention: '7d'
    }
  },

  // Security configuration
  security: {
    allowedOrigins: [
      // Will be set based on ORIGIN environment variable
    ],
    rateLimiting: {
      enabled: true,
      requests: 100,
      window: '15m'
    },
    headers: {
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin'
    }
  }
};
