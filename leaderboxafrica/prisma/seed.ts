// Database Seed Script
// Populates the database with initial data for development

import { PrismaClient } from '@prisma/client';
import { hashPassword } from '../src/lib/server/password.js';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Hash passwords for demo accounts
  const adminPasswordHash = await hashPassword('adminpass123');
  const userPasswordHash = await hashPassword('password123');

  // Create sample users
  const users = await Promise.all([
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'Admin',
        isAdmin: true,
        state: 'Lagos',
        lga: 'Ikeja',
        gender: 'Male',
        party: 'Neutral',
        onboardingComplete: true,
        passwordHash: adminPasswordHash,
      },
    }),

    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Leader<PERSON>ox Admin',
        role: 'Admin',
        isAdmin: true,
        // emailVerified: true,
        state: 'Lagos',
        lga: 'Ikeja',
        gender: 'Male',
        party: 'Neutral',
        onboardingComplete: true,
        passwordHash: adminPasswordHash,
      },
    }),

    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Demo User',
        role: 'User',
        isAdmin: false,
        state: 'Lagos',
        lga: 'Victoria Island',
        gender: 'Female',
        party: 'Neutral',
        onboardingComplete: true,
        passwordHash: userPasswordHash,
      },
    }),
  ]);

  console.log('✅ Created users:', users.length);

  // Create sample leaders
  const leaders = await Promise.all([
    prisma.leader.upsert({
      where: { id: 'leader-1' },
      update: {},
      create: {
        id: 'leader-1',
        name: 'Bola Ahmed Tinubu',
        position: 'President',
        party: 'APC',
        state: 'Lagos',
        bio: 'Current President of the Federal Republic of Nigeria',
        detailedBio: 'Bola Ahmed Tinubu is a Nigerian politician who has served as the President of Nigeria since 2023...',
        ratingAverage: 3.8,
        ratingCount: 450,
        followersCount: 1250,
      },
    }),
    prisma.leader.upsert({
      where: { id: 'leader-2' },
      update: {},
      create: {
        id: 'leader-2',
        name: 'Peter Obi',
        position: 'Former Governor',
        party: 'LP',
        state: 'Anambra',
        bio: 'Former Governor of Anambra State and 2023 Presidential Candidate',
        detailedBio: 'Peter Gregory Obi is a Nigerian businessman and politician who served as Governor of Anambra State...',
        ratingAverage: 4.2,
        ratingCount: 680,
        followersCount: 2100,
      },
    }),
    prisma.leader.upsert({
      where: { id: 'leader-3' },
      update: {},
      create: {
        id: 'leader-3',
        name: 'Atiku Abubakar',
        position: 'Former Vice President',
        party: 'PDP',
        state: 'Adamawa',
        bio: 'Former Vice President of Nigeria and 2023 Presidential Candidate',
        detailedBio: 'Atiku Abubakar is a Nigerian politician and businessman who served as Vice President of Nigeria...',
        ratingAverage: 3.5,
        ratingCount: 520,
        followersCount: 980,
      },
    }),
  ]);

  console.log('✅ Created leaders:', leaders.length);

  // Create sample polls
  const polls = await Promise.all([
    prisma.poll.create({
      data: {
        topic: 'Should Nigeria invest more in renewable energy?',
        description: 'Considering Nigeria\'s current energy crisis and climate change concerns, should the government prioritize renewable energy investments?',
        creatorId: users[0].id,
        totalVotes: 115,
        upvotes: 25,
        downvotes: 3,
        options: {
          create: [
            {
              text: 'Yes, prioritize renewable energy',
              votes: 75,
              optionOrder: 0,
            },
            {
              text: 'No, focus on traditional energy',
              votes: 40,
              optionOrder: 1,
            },
          ],
        },
      },
    }),
    prisma.poll.create({
      data: {
        topic: 'What should be the government\'s top priority?',
        description: 'In your opinion, what area should the Nigerian government focus on most urgently?',
        creatorId: users[0].id,
        totalVotes: 89,
        upvotes: 18,
        downvotes: 2,
        options: {
          create: [
            {
              text: 'Economy and Job Creation',
              votes: 35,
              optionOrder: 0,
            },
            {
              text: 'Security and Safety',
              votes: 28,
              optionOrder: 1,
            },
            {
              text: 'Education Reform',
              votes: 15,
              optionOrder: 2,
            },
            {
              text: 'Healthcare Improvement',
              votes: 11,
              optionOrder: 3,
            },
          ],
        },
      },
    }),
  ]);

  console.log('✅ Created polls:', polls.length);

  // Create sample banters
  const banters = await Promise.all([
    prisma.banter.create({
      data: {
        authorId: users[1].id,
        content: 'Just watched the latest presidential address. What are your thoughts on the new economic policies? #NigerianPolitics',
        upvotes: 12,
        downvotes: 2,
        commentsCount: 5,
      },
    }),
    prisma.banter.create({
      data: {
        authorId: users[0].id,
        content: 'Democracy works best when citizens are actively engaged. Let\'s keep the conversation going! 🇳🇬',
        upvotes: 8,
        downvotes: 0,
        commentsCount: 3,
      },
    }),
  ]);

  console.log('✅ Created banters:', banters.length);

  // Create sample petitions
  const petitions = await Promise.all([
    prisma.petition.create({
      data: {
        title: 'Improve Public Transportation in Lagos',
        description: 'We petition the Lagos State Government to invest more in public transportation infrastructure to reduce traffic congestion and improve quality of life for residents.',
        creatorId: users[1].id,
        signatures: 1250,
        status: 'active',
      },
    }),
    prisma.petition.create({
      data: {
        title: 'Increase Funding for Public Education',
        description: 'We call on the Federal Government to increase budget allocation for public education to ensure quality education for all Nigerian children.',
        creatorId: users[0].id,
        signatures: 890,
        status: 'active',
      },
    }),
  ]);

  console.log('✅ Created petitions:', petitions.length);

  // Create sample groups
  const groups = await Promise.all([
    prisma.group.create({
      data: {
        name: 'Lagos Youth Political Forum',
        description: 'A platform for young people in Lagos to discuss political issues and engage in civic activities.',
        creatorId: users[1].id,
        isPublic: true,
        status: 'visible',
        members: {
          create: [
            {
              userId: users[1].id,
              role: 'admin',
            },
            {
              userId: users[0].id,
              role: 'member',
            },
          ],
        },
      },
    }),
    prisma.group.create({
      data: {
        name: 'Nigerian Economic Policy Discussions',
        description: 'Discussing economic policies and their impact on Nigerian citizens.',
        creatorId: users[0].id,
        isPublic: true,
        status: 'visible',
        members: {
          create: [
            {
              userId: users[0].id,
              role: 'admin',
            },
          ],
        },
      },
    }),
  ]);

  console.log('✅ Created groups:', groups.length);

  // Create RBAC permissions
  const permissions = await Promise.all([
    // User permissions
    prisma.permission.upsert({
      where: { name: 'users:read' },
      update: {},
      create: {
        name: 'users:read',
        resource: 'users',
        action: 'read',
        description: 'View user profiles and lists',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'users:create' },
      update: {},
      create: {
        name: 'users:create',
        resource: 'users',
        action: 'create',
        description: 'Create new user accounts',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'users:update' },
      update: {},
      create: {
        name: 'users:update',
        resource: 'users',
        action: 'update',
        description: 'Edit user information',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'users:delete' },
      update: {},
      create: {
        name: 'users:delete',
        resource: 'users',
        action: 'delete',
        description: 'Delete user accounts',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'users:manage' },
      update: {},
      create: {
        name: 'users:manage',
        resource: 'users',
        action: 'manage',
        description: 'Full user management access',
      },
    }),

    // Leader permissions
    prisma.permission.upsert({
      where: { name: 'leaders:read' },
      update: {},
      create: {
        name: 'leaders:read',
        resource: 'leaders',
        action: 'read',
        description: 'View leader profiles',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'leaders:create' },
      update: {},
      create: {
        name: 'leaders:create',
        resource: 'leaders',
        action: 'create',
        description: 'Add new leaders',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'leaders:update' },
      update: {},
      create: {
        name: 'leaders:update',
        resource: 'leaders',
        action: 'update',
        description: 'Edit leader information',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'leaders:delete' },
      update: {},
      create: {
        name: 'leaders:delete',
        resource: 'leaders',
        action: 'delete',
        description: 'Remove leaders',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'leaders:moderate' },
      update: {},
      create: {
        name: 'leaders:moderate',
        resource: 'leaders',
        action: 'moderate',
        description: 'Moderate leader content',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'leaders:manage' },
      update: {},
      create: {
        name: 'leaders:manage',
        resource: 'leaders',
        action: 'manage',
        description: 'Full leader management access',
      },
    }),

    // Content permissions
    prisma.permission.upsert({
      where: { name: 'content:read' },
      update: {},
      create: {
        name: 'content:read',
        resource: 'content',
        action: 'read',
        description: 'View all content',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'content:create' },
      update: {},
      create: {
        name: 'content:create',
        resource: 'content',
        action: 'create',
        description: 'Create content',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'content:update' },
      update: {},
      create: {
        name: 'content:update',
        resource: 'content',
        action: 'update',
        description: 'Edit content',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'content:delete' },
      update: {},
      create: {
        name: 'content:delete',
        resource: 'content',
        action: 'delete',
        description: 'Delete content',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'content:moderate' },
      update: {},
      create: {
        name: 'content:moderate',
        resource: 'content',
        action: 'moderate',
        description: 'Moderate user content',
      },
    }),

    // Leader permissions with scope
    prisma.permission.upsert({
      where: { name: 'leaders:update:own' },
      update: {},
      create: {
        name: 'leaders:update:own',
        resource: 'leaders',
        action: 'update',
        scope: 'own',
        description: 'Edit own leader profiles',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'leaders:delete:own' },
      update: {},
      create: {
        name: 'leaders:delete:own',
        resource: 'leaders',
        action: 'delete',
        scope: 'own',
        description: 'Delete own leader profiles',
      },
    }),

    // Staff user management
    prisma.permission.upsert({
      where: { name: 'users:create:staff' },
      update: {},
      create: {
        name: 'users:create:staff',
        resource: 'users',
        action: 'create',
        scope: 'staff',
        description: 'Create staff user accounts',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'users:manage:staff' },
      update: {},
      create: {
        name: 'users:manage:staff',
        resource: 'users',
        action: 'manage',
        scope: 'staff',
        description: 'Manage staff user accounts',
      },
    }),

    // Admin permissions
    prisma.permission.upsert({
      where: { name: 'admin:access' },
      update: {},
      create: {
        name: 'admin:access',
        resource: 'admin',
        action: 'access',
        description: 'Access admin dashboard',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'admin:settings' },
      update: {},
      create: {
        name: 'admin:settings',
        resource: 'admin',
        action: 'settings',
        description: 'Modify system settings',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'admin:settings:sensitive' },
      update: {},
      create: {
        name: 'admin:settings:sensitive',
        resource: 'admin',
        action: 'settings',
        scope: 'sensitive',
        description: 'Modify sensitive system settings',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'admin:audit' },
      update: {},
      create: {
        name: 'admin:audit',
        resource: 'admin',
        action: 'audit',
        description: 'View audit logs',
      },
    }),
    prisma.permission.upsert({
      where: { name: 'admin:roles' },
      update: {},
      create: {
        name: 'admin:roles',
        resource: 'admin',
        action: 'roles',
        description: 'Manage roles and permissions',
      },
    }),
  ]);

  console.log('✅ Created permissions:', permissions.length);

  // Create default roles
  const userRole = await prisma.role.upsert({
    where: { name: 'User' },
    update: {},
    create: {
      name: 'User',
      description: 'Standard user with basic permissions',
      isSystem: true,
    },
  });

  const writerRole = await prisma.role.upsert({
    where: { name: 'Writer' },
    update: {},
    create: {
      name: 'Writer',
      description: 'Content writer who can create and edit their own leader profiles',
      isSystem: true,
    },
  });

  const moderatorRole = await prisma.role.upsert({
    where: { name: 'Moderator' },
    update: {},
    create: {
      name: 'Moderator',
      description: 'Content moderator with limited admin access',
      isSystem: true,
    },
  });

  const editorRole = await prisma.role.upsert({
    where: { name: 'Editor' },
    update: {},
    create: {
      name: 'Editor',
      description: 'Content editor with full content management access except staff user creation and sensitive settings',
      isSystem: true,
    },
  });

  const adminRole = await prisma.role.upsert({
    where: { name: 'Admin' },
    update: {},
    create: {
      name: 'Admin',
      description: 'Full system administrator with all permissions',
      isSystem: true,
    },
  });

  console.log('✅ Created roles: User, Writer, Moderator, Editor, Admin');

  // Assign permissions to User role - basic read permissions
  const userPermissions = permissions.filter(p => 
    ['content:read', 'leaders:read', 'users:read'].includes(p.name)
  );
  
  for (const permission of userPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: userRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: userRole.id,
        permissionId: permission.id,
      },
    });
  }

  // Assign permissions to Writer role - can create/edit own leader profiles
  const writerPermissions = permissions.filter(p => 
    [
      'content:read', 'content:create',
      'leaders:read', 'leaders:create', 'leaders:update:own', 'leaders:delete:own',
      'users:read'
    ].includes(p.name)
  );
  
  for (const permission of writerPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: writerRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: writerRole.id,
        permissionId: permission.id,
      },
    });
  }

  // Assign permissions to Moderator role - content moderation permissions
  const moderatorPermissions = permissions.filter(p => 
    [
      'content:read', 'content:moderate',
      'leaders:read', 'leaders:moderate',
      'users:read'
    ].includes(p.name)
  );
  
  for (const permission of moderatorPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: moderatorRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: moderatorRole.id,
        permissionId: permission.id,
      },
    });
  }

  // Assign permissions to Editor role - everything except staff user creation and sensitive settings
  const editorPermissions = permissions.filter(p => 
    ![
      'users:create:staff', 'users:manage:staff', 'admin:settings:sensitive'
    ].includes(p.name)
  );
  
  for (const permission of editorPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: editorRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: editorRole.id,
        permissionId: permission.id,
      },
    });
  }

  // Assign all permissions to Admin role
  for (const permission of permissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: adminRole.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: adminRole.id,
        permissionId: permission.id,
      },
    });
  }

  console.log('✅ Assigned permissions to roles');

  // Update existing users with RBAC roles
  await prisma.user.update({
    where: { email: '<EMAIL>' },
    data: { roleId: adminRole.id },
  });

  await prisma.user.update({
    where: { email: '<EMAIL>' },
    data: { roleId: adminRole.id },
  });

  await prisma.user.update({
    where: { email: '<EMAIL>' },
    data: { roleId: userRole.id },
  });

  // Create UserRole records for audit trail
  const adminUser1 = users.find(u => u.email === '<EMAIL>');
  const adminUser2 = users.find(u => u.email === '<EMAIL>');
  const regularUser = users.find(u => u.email === '<EMAIL>');

  if (adminUser1) {
    await prisma.userRole.upsert({
      where: {
        userId_roleId_isActive: {
          userId: adminUser1.id,
          roleId: adminRole.id,
          isActive: true,
        },
      },
      update: {},
      create: {
        userId: adminUser1.id,
        roleId: adminRole.id,
        assignedBy: adminUser1.id, // Self-assigned for initial setup
        isActive: true,
      },
    });
  }

  if (adminUser2) {
    await prisma.userRole.upsert({
      where: {
        userId_roleId_isActive: {
          userId: adminUser2.id,
          roleId: adminRole.id,
          isActive: true,
        },
      },
      update: {},
      create: {
        userId: adminUser2.id,
        roleId: adminRole.id,
        assignedBy: adminUser2.id, // Self-assigned for initial setup
        isActive: true,
      },
    });
  }

  if (regularUser) {
    await prisma.userRole.upsert({
      where: {
        userId_roleId_isActive: {
          userId: regularUser.id,
          roleId: userRole.id,
          isActive: true,
        },
      },
      update: {},
      create: {
        userId: regularUser.id,
        roleId: userRole.id,
        assignedBy: adminUser1?.id || adminUser2?.id || regularUser.id,
        isActive: true,
      },
    });
  }

  console.log('✅ Assigned RBAC roles to existing users');

  // Create initial audit log entries
  await prisma.auditLog.createMany({
    data: [
      {
        userId: adminUser1?.id,
        action: 'system_initialization',
        resource: 'rbac',
        details: {
          message: 'RBAC system initialized with default roles and permissions',
          roles_created: ['User', 'Writer', 'Editor', 'Admin'],
          permissions_created: permissions.length,
        },
        success: true,
      },
      {
        userId: adminUser1?.id,
        action: 'role_assigned',
        resource: 'user',
        resourceId: adminUser1?.id,
        details: {
          role: 'Admin',
          assigned_by: 'system',
          reason: 'Initial setup',
        },
        success: true,
      },
      {
        userId: adminUser2?.id,
        action: 'role_assigned',
        resource: 'user',
        resourceId: adminUser2?.id,
        details: {
          role: 'Admin',
          assigned_by: 'system',
          reason: 'Initial setup',
        },
        success: true,
      },
      {
        userId: regularUser?.id,
        action: 'role_assigned',
        resource: 'user',
        resourceId: regularUser?.id,
        details: {
          role: 'User',
          assigned_by: 'system',
          reason: 'Initial setup',
        },
        success: true,
      },
    ],
  });

  console.log('✅ Created initial audit log entries');

  console.log('🎉 Database seed completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
