#!/usr/bin/env node

/**
 * Assign Staff Management Permissions to Admin Role
 * 
 * Assigns users:create:staff and users:manage:staff permissions to the Admin role
 * so that admin users can manage staff members.
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function assignStaffPermissionsToAdmin() {
  console.log('👥 Assigning staff management permissions to Admin role...');
  
  try {
    // Get the Admin role
    const adminRole = await prisma.role.findUnique({
      where: { name: 'Admin' },
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      }
    });
    
    if (!adminRole) {
      throw new Error('Admin role not found. Please run the optimization script first.');
    }
    
    console.log(`Found Admin role with ${adminRole.permissions.length} existing permissions`);
    
    // Get the staff management permissions
    const staffPermissions = await prisma.permission.findMany({
      where: {
        name: {
          in: ['users:create:staff', 'users:manage:staff']
        }
      }
    });
    
    if (staffPermissions.length === 0) {
      console.log('❌ Staff management permissions not found. Creating them...');
      
      // Create the missing permissions
      const createdPermissions = await Promise.all([
        prisma.permission.upsert({
          where: { name: 'users:create:staff' },
          update: {},
          create: {
            name: 'users:create:staff',
            resource: 'users',
            action: 'create',
            scope: 'staff',
            description: 'Create staff user accounts'
          }
        }),
        prisma.permission.upsert({
          where: { name: 'users:manage:staff' },
          update: {},
          create: {
            name: 'users:manage:staff',
            resource: 'users',
            action: 'manage',
            scope: 'staff',
            description: 'Manage staff user accounts'
          }
        })
      ]);
      
      staffPermissions.push(...createdPermissions);
      console.log(`✅ Created ${createdPermissions.length} staff management permissions`);
    }
    
    console.log(`Found ${staffPermissions.length} staff management permissions to assign`);
    
    // Check which permissions are already assigned
    const existingPermissionIds = new Set(adminRole.permissions.map(rp => rp.permission.id));
    const permissionsToAssign = staffPermissions.filter(p => !existingPermissionIds.has(p.id));
    
    if (permissionsToAssign.length === 0) {
      console.log('✅ Admin role already has all staff management permissions!');
      return;
    }
    
    // Assign the permissions to Admin role
    const assignments = await Promise.all(
      permissionsToAssign.map(permission =>
        prisma.rolePermission.create({
          data: {
            roleId: adminRole.id,
            permissionId: permission.id
          }
        })
      )
    );
    
    console.log(`✅ Assigned ${assignments.length} staff management permissions to Admin role:`);
    
    for (const permission of permissionsToAssign) {
      console.log(`   • ${permission.name} - ${permission.description}`);
    }
    
    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'role_permissions_updated',
        resource: 'role',
        resourceId: adminRole.id,
        details: {
          message: 'Assigned staff management permissions to Admin role',
          role_name: 'Admin',
          permissions_added: permissionsToAssign.map(p => p.name),
          total_permissions_added: permissionsToAssign.length,
          timestamp: new Date().toISOString(),
          reason: 'Admin needs to manage staff members'
        },
        success: true
      }
    });
    
    // Verify the assignment
    const updatedAdminRole = await prisma.role.findUnique({
      where: { name: 'Admin' },
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      }
    });
    
    const hasStaffCreate = updatedAdminRole?.permissions.some(rp => rp.permission.name === 'users:create:staff');
    const hasStaffManage = updatedAdminRole?.permissions.some(rp => rp.permission.name === 'users:manage:staff');
    
    console.log('\n🔍 Verification:');
    console.log(`   Admin role now has ${updatedAdminRole?.permissions.length} total permissions`);
    console.log(`   Has 'users:create:staff': ${hasStaffCreate ? '✅' : '❌'}`);
    console.log(`   Has 'users:manage:staff': ${hasStaffManage ? '✅' : '❌'}`);
    
    if (hasStaffCreate && hasStaffManage) {
      console.log('\n🎉 Success! Admin users can now manage staff members.');
      console.log('\nAdmin users now have the following staff management capabilities:');
      console.log('   • Create staff user accounts');
      console.log('   • Manage staff user accounts');
      console.log('   • Full administrative control over staff members');
    } else {
      console.log('\n❌ Something went wrong. Please check the database manually.');
    }
    
  } catch (error) {
    console.error('❌ Failed to assign staff permissions to Admin role:', error);
    throw error;
  }
}

// Run the script
assignStaffPermissionsToAdmin()
  .catch((e) => {
    console.error('❌ Script failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });