#!/usr/bin/env node

/**
 * Test Staff Management Permissions
 * 
 * Tests if admin users have the correct staff management permissions
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testStaffPermissions() {
  console.log('👥 Testing Staff Management Permissions...\n');
  
  try {
    // Get admin users
    const adminUsers = await prisma.user.findMany({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ]
      },
      include: {
        userRole: {
          include: {
            permissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    });
    
    console.log(`Found ${adminUsers.length} admin users to test:\n`);
    
    const staffPermissions = ['users:create:staff', 'users:manage:staff'];
    
    for (const user of adminUsers) {
      console.log(`👤 Testing user: ${user.email}`);
      console.log(`   Role: ${user.userRole?.name || 'No role assigned'}`);
      
      if (!user.userRole) {
        console.log('   ❌ No role assigned - this user cannot manage staff');
        continue;
      }
      
      // Get user permissions
      const permissions = user.userRole.permissions.map(rp => rp.permission.name);
      console.log(`   Total Permissions: ${permissions.length}`);
      
      // Test staff management permissions
      console.log('   Staff Management Permissions:');
      
      for (const staffPermission of staffPermissions) {
        const hasPermission = permissions.includes(staffPermission);
        console.log(`   ${hasPermission ? '✅' : '❌'} ${staffPermission}`);
      }
      
      // Overall staff management capability
      const canManageStaff = staffPermissions.every(permission => permissions.includes(permission));
      console.log(`   \n   🎯 Can Manage Staff: ${canManageStaff ? '✅ YES' : '❌ NO'}`);
      
      console.log(''); // Empty line for readability
    }
    
    // Summary
    console.log('📊 Staff Management Summary:');
    console.log('============================');
    
    const adminUsersWithStaffAccess = adminUsers.filter(user => {
      if (!user.userRole) return false;
      const permissions = user.userRole.permissions.map(rp => rp.permission.name);
      return staffPermissions.every(permission => permissions.includes(permission));
    });
    
    console.log(`✅ Admin users with staff management access: ${adminUsersWithStaffAccess.length}/${adminUsers.length}`);
    
    if (adminUsersWithStaffAccess.length === adminUsers.length) {
      console.log('🎉 All admin users can manage staff members!');
      
      console.log('\nStaff Management Capabilities:');
      console.log('• ✅ Create staff user accounts');
      console.log('• ✅ Manage existing staff accounts');
      console.log('• ✅ Update staff user information');
      console.log('• ✅ Assign roles to staff members');
      console.log('• ✅ Full administrative control over staff');
    } else {
      console.log('⚠️  Some admin users cannot manage staff. Please check role assignments.');
    }
    
    console.log('\n✅ Staff permission test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run the test
testStaffPermissions()
  .catch((e) => {
    console.error('❌ Test failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });