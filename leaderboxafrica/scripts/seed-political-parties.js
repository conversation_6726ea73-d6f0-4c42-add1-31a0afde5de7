#!/usr/bin/env node

/**
 * Political Parties Seeding Script
 * 
 * Seeds the political_parties table with comprehensive data for Nigerian political parties.
 * Includes major parties, regional parties, and historical parties with detailed information.
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Comprehensive Nigerian Political Parties Data
const NIGERIAN_POLITICAL_PARTIES = [
    // Major National Parties
    {
        name: 'All Progressives Congress',
        abbreviation: 'APC',
        description: 'The ruling political party in Nigeria, formed in 2013 through a merger of several opposition parties.',
        foundedYear: 2013,
        websiteUrl: 'https://www.apc.ng',
        headquarters: 'Abuja, FCT',
        ideology: 'Progressive Conservatism',
        isActive: true
    },
    {
        name: 'Peoples Democratic Party',
        abbreviation: 'PDP',
        description: 'One of Nigeria\'s major political parties, ruled Nigeria from 1999 to 2015.',
        foundedYear: 1998,
        websiteUrl: 'https://www.pdp.org.ng',
        headquarters: 'Abuja, FCT',
        ideology: 'Social Democracy',
        isActive: true
    },
    {
        name: 'Labour Party',
        abbreviation: 'LP',
        description: 'A political party that gained significant prominence in the 2023 general elections.',
        foundedYear: 2002,
        websiteUrl: 'https://www.labourparty.ng',
        headquarters: 'Abuja, FCT',
        ideology: 'Social Democracy, Labour Rights',
        isActive: true
    },
    {
        name: 'New Nigeria Peoples Party',
        abbreviation: 'NNPP',
        description: 'A political party formed to provide alternative leadership for Nigeria.',
        foundedYear: 2018,
        headquarters: 'Abuja, FCT',
        ideology: 'Progressive Politics',
        isActive: true
    },

    // Other Registered Parties
    {
        name: 'All Progressives Grand Alliance',
        abbreviation: 'APGA',
        description: 'A political party with strong presence in the South-East region of Nigeria.',
        foundedYear: 2002,
        headquarters: 'Awka, Anambra State',
        ideology: 'Igbo Nationalism, Federalism',
        isActive: true
    },
    {
        name: 'Social Democratic Party',
        abbreviation: 'SDP',
        description: 'A centrist political party advocating for social democracy in Nigeria.',
        foundedYear: 2018,
        headquarters: 'Abuja, FCT',
        ideology: 'Social Democracy',
        isActive: true
    },
    {
        name: 'Young Progressive Party',
        abbreviation: 'YPP',
        description: 'A political party focused on youth participation and progressive governance.',
        foundedYear: 2017,
        headquarters: 'Abuja, FCT',
        ideology: 'Youth Empowerment, Progressive Politics',
        isActive: true
    },
    {
        name: 'African Action Congress',
        abbreviation: 'AAC',
        description: 'A political party advocating for revolutionary change and social justice.',
        foundedYear: 2018,
        headquarters: 'Lagos State',
        ideology: 'Revolutionary Socialism',
        isActive: true
    },
    {
        name: 'Accord Party',
        abbreviation: 'AP',
        description: 'A political party promoting unity and national development.',
        foundedYear: 2006,
        headquarters: 'Ibadan, Oyo State',
        ideology: 'National Unity',
        isActive: true
    },
    {
        name: 'Action Alliance',
        abbreviation: 'AA',
        description: 'A political party focused on democratic governance and development.',
        foundedYear: 2005,
        headquarters: 'Abuja, FCT',
        ideology: 'Democratic Governance',
        isActive: true
    },

    // Regional and Smaller Parties
    {
        name: 'Action Democratic Party',
        abbreviation: 'ADP',
        description: 'A political party advocating for democratic reforms and good governance.',
        foundedYear: 2017,
        headquarters: 'Abuja, FCT',
        ideology: 'Democratic Reform',
        isActive: true
    },
    {
        name: 'Allied Peoples Movement',
        abbreviation: 'APM',
        description: 'A political party promoting unity among Nigerian peoples.',
        foundedYear: 2018,
        headquarters: 'Abuja, FCT',
        ideology: 'National Unity',
        isActive: true
    },
    {
        name: 'Boot Party',
        abbreviation: 'BP',
        description: 'A political party focused on youth mobilization and change.',
        foundedYear: 2018,
        headquarters: 'Lagos State',
        ideology: 'Youth Mobilization',
        isActive: true
    },
    {
        name: 'New Nigeria Peoples Party',
        abbreviation: 'NNPP',
        description: 'A political party advocating for new leadership and governance approach.',
        foundedYear: 2018,
        headquarters: 'Kano State',
        ideology: 'New Leadership',
        isActive: true
    },
    {
        name: 'Peoples Redemption Party',
        abbreviation: 'PRP',
        description: 'A political party with historical roots in Northern Nigeria.',
        foundedYear: 1978,
        headquarters: 'Kano State',
        ideology: 'Social Justice, Northern Interests',
        isActive: true
    },

    // Historical/Legacy Parties
    {
        name: 'All Nigeria Peoples Party',
        abbreviation: 'ANPP',
        description: 'A defunct political party that was part of the APC merger in 2013.',
        foundedYear: 1998,
        headquarters: 'Abuja, FCT',
        ideology: 'Conservative Politics',
        isActive: false
    },
    {
        name: 'Congress for Progressive Change',
        abbreviation: 'CPC',
        description: 'A defunct political party that merged to form APC in 2013.',
        foundedYear: 2009,
        headquarters: 'Abuja, FCT',
        ideology: 'Progressive Change',
        isActive: false
    },
    {
        name: 'Action Congress of Nigeria',
        abbreviation: 'ACN',
        description: 'A defunct political party that was part of the APC merger in 2013.',
        foundedYear: 2006,
        headquarters: 'Lagos State',
        ideology: 'Progressive Politics',
        isActive: false
    },
    {
        name: 'National Party of Nigeria',
        abbreviation: 'NPN',
        description: 'A historical political party from Nigeria\'s Second Republic (1979-1983).',
        foundedYear: 1978,
        headquarters: 'Lagos State',
        ideology: 'Conservative Politics',
        isActive: false
    },
    {
        name: 'Unity Party of Nigeria',
        abbreviation: 'UPN',
        description: 'A historical political party led by Chief Obafemi Awolowo.',
        foundedYear: 1978,
        headquarters: 'Ibadan, Oyo State',
        ideology: 'Democratic Socialism',
        isActive: false
    },

    // Emerging and Newer Parties
    {
        name: 'Zenith Labour Party',
        abbreviation: 'ZLP',
        description: 'A political party focused on workers\' rights and social justice.',
        foundedYear: 2017,
        headquarters: 'Lagos State',
        ideology: 'Labour Rights, Social Justice',
        isActive: true
    },
    {
        name: 'National Rescue Movement',
        abbreviation: 'NRM',
        description: 'A political party advocating for national rescue and transformation.',
        foundedYear: 2018,
        headquarters: 'Abuja, FCT',
        ideology: 'National Transformation',
        isActive: true
    },
    {
        name: 'Peoples Trust',
        abbreviation: 'PT',
        description: 'A political party built on trust and integrity in governance.',
        foundedYear: 2018,
        headquarters: 'Abuja, FCT',
        ideology: 'Integrity in Governance',
        isActive: true
    },
    {
        name: 'Advanced Allied Party',
        abbreviation: 'AAP',
        description: 'A political party promoting advanced democratic ideals.',
        foundedYear: 2017,
        headquarters: 'Abuja, FCT',
        ideology: 'Advanced Democracy',
        isActive: true
    },
    {
        name: 'Democratic Peoples Party',
        abbreviation: 'DPP',
        description: 'A political party advocating for democratic governance and people\'s welfare.',
        foundedYear: 2018,
        headquarters: 'Abuja, FCT',
        ideology: 'Democratic Governance',
        isActive: true
    }
];

async function seedPoliticalParties() {
    console.log('🏛️  Seeding Political Parties...\n');

    try {
        // Get admin user for created_by field
        const adminUser = await prisma.user.findFirst({
            where: {
                OR: [
                    { email: '<EMAIL>' },
                    { email: '<EMAIL>' }
                ]
            }
        });

        if (!adminUser) {
            console.log('⚠️  No admin user found. Parties will be created without creator reference.');
        }

        console.log(`📊 Processing ${NIGERIAN_POLITICAL_PARTIES.length} political parties...`);

        let createdCount = 0;
        let updatedCount = 0;
        let skippedCount = 0;

        for (const partyData of NIGERIAN_POLITICAL_PARTIES) {
            try {
                // Check if party already exists
                const existingParty = await prisma.politicalParty.findFirst({
                    where: {
                        OR: [
                            { name: partyData.name },
                            { abbreviation: partyData.abbreviation }
                        ]
                    }
                });

                if (existingParty) {
                    // Update existing party with new information
                    await prisma.politicalParty.update({
                        where: { id: existingParty.id },
                        data: {
                            ...partyData,
                            updatedBy: adminUser?.id,
                            updatedAt: new Date()
                        }
                    });

                    updatedCount++;
                    console.log(`   ✅ Updated: ${partyData.name} (${partyData.abbreviation})`);
                } else {
                    // Create new party
                    await prisma.politicalParty.create({
                        data: {
                            ...partyData,
                            createdBy: adminUser?.id,
                            updatedBy: adminUser?.id
                        }
                    });

                    createdCount++;
                    console.log(`   🆕 Created: ${partyData.name} (${partyData.abbreviation})`);
                }

            } catch (error) {
                skippedCount++;
                console.log(`   ⚠️  Skipped: ${partyData.name} - ${error.message}`);
            }
        }

        // Get statistics
        const totalParties = await prisma.politicalParty.count();
        const activeParties = await prisma.politicalParty.count({
            where: { isActive: true }
        });
        const inactiveParties = await prisma.politicalParty.count({
            where: { isActive: false }
        });

        // Create audit log
        await prisma.auditLog.create({
            data: {
                userId: adminUser?.id,
                action: 'political_parties_seeded',
                resource: 'political_parties',
                details: {
                    message: 'Political parties database seeded',
                    parties_created: createdCount,
                    parties_updated: updatedCount,
                    parties_skipped: skippedCount,
                    total_parties: totalParties,
                    active_parties: activeParties,
                    inactive_parties: inactiveParties,
                    timestamp: new Date().toISOString()
                },
                success: true
            }
        });

        console.log('\n📊 Seeding Summary:');
        console.log('===================');
        console.log(`✅ Created: ${createdCount} parties`);
        console.log(`🔄 Updated: ${updatedCount} parties`);
        console.log(`⚠️  Skipped: ${skippedCount} parties`);
        console.log(`📈 Total in Database: ${totalParties} parties`);
        console.log(`🟢 Active Parties: ${activeParties}`);
        console.log(`🔴 Inactive/Historical: ${inactiveParties}`);

        console.log('\n🏛️  Major Active Parties:');
        const majorParties = await prisma.politicalParty.findMany({
            where: {
                isActive: true,
                abbreviation: { in: ['APC', 'PDP', 'LP', 'NNPP', 'APGA'] }
            },
            orderBy: { foundedYear: 'asc' }
        });

        for (const party of majorParties) {
            console.log(`   • ${party.name} (${party.abbreviation}) - Founded ${party.foundedYear}`);
        }

        console.log('\n🎉 Political parties seeding completed successfully!');

        console.log('\n💡 Next Steps:');
        console.log('   • Update leader profiles to reference these parties');
        console.log('   • Create party-based filtering in the admin interface');
        console.log('   • Add party logos and additional metadata');
        console.log('   • Implement party-based analytics and reporting');

    } catch (error) {
        console.error('❌ Political parties seeding failed:', error);
        throw error;
    }
}

// Run the seeding
seedPoliticalParties()
    .catch((e) => {
        console.error('❌ Seeding failed:', e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });