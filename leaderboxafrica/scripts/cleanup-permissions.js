#!/usr/bin/env node

/**
 * Cleanup Orphaned Permissions Script
 * 
 * Removes or assigns orphaned permissions that aren't assigned to any role
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanupPermissions() {
  console.log('🧹 Cleaning up orphaned permissions...');
  
  try {
    // Find orphaned permissions
    const orphanedPermissions = await prisma.permission.findMany({
      where: {
        roles: {
          none: {}
        }
      }
    });
    
    if (orphanedPermissions.length === 0) {
      console.log('✅ No orphaned permissions found!');
      return;
    }
    
    console.log(`Found ${orphanedPermissions.length} orphaned permissions:`);
    
    // Get the Manager role (should have staff management permissions)
    const managerRole = await prisma.role.findUnique({
      where: { name: 'Manager' }
    });
    
    if (!managerRole) {
      console.log('❌ Manager role not found. Cannot assign staff permissions.');
      return;
    }
    
    let assignedCount = 0;
    let removedCount = 0;
    
    for (const permission of orphanedPermissions) {
      console.log(`   Processing: ${permission.name}`);
      
      // Assign staff-related permissions to Manager role
      if (permission.name.includes('staff')) {
        await prisma.rolePermission.create({
          data: {
            roleId: managerRole.id,
            permissionId: permission.id
          }
        });
        
        assignedCount++;
        console.log(`   ✅ Assigned to Manager role`);
      } else {
        // Remove truly orphaned permissions that don't belong anywhere
        await prisma.permission.delete({
          where: { id: permission.id }
        });
        
        removedCount++;
        console.log(`   🗑️  Removed orphaned permission`);
      }
    }
    
    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'permission_cleanup',
        resource: 'permissions',
        details: {
          message: 'Cleaned up orphaned permissions',
          assigned_to_manager: assignedCount,
          removed_permissions: removedCount,
          total_processed: orphanedPermissions.length,
          timestamp: new Date().toISOString()
        },
        success: true
      }
    });
    
    console.log(`\n🎉 Cleanup completed:`);
    console.log(`   • ${assignedCount} permissions assigned to Manager role`);
    console.log(`   • ${removedCount} permissions removed`);
    console.log(`   • Total processed: ${orphanedPermissions.length}`);
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    throw error;
  }
}

// Run the cleanup
cleanupPermissions()
  .catch((e) => {
    console.error('❌ Cleanup failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });