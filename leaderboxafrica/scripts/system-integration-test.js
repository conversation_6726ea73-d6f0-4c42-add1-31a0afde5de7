#!/usr/bin/env node

/**
 * System Integration Test
 * 
 * Comprehensive test to verify all systems are working together:
 * - RBAC system functionality
 * - Political parties integration
 * - Admin access and permissions
 * - Database consistency
 * - API endpoints functionality
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function runSystemIntegrationTest() {
  console.log('🧪 Running System Integration Test...\n');
  
  const testResults = {
    rbac: { passed: 0, failed: 0, tests: [] },
    politicalParties: { passed: 0, failed: 0, tests: [] },
    adminAccess: { passed: 0, failed: 0, tests: [] },
    dataIntegrity: { passed: 0, failed: 0, tests: [] }
  };

  try {
    // 1. RBAC System Tests
    console.log('🔐 Testing RBAC System...');
    console.log('========================');
    
    // Test 1.1: Admin users have proper roles
    try {
      const adminUsers = await prisma.user.findMany({
        where: {
          OR: [
            { email: '<EMAIL>' },
            { email: '<EMAIL>' }
          ]
        },
        include: {
          userRole: {
            include: {
              permissions: {
                include: {
                  permission: true
                }
              }
            }
          }
        }
      });
      
      let adminTestPassed = true;
      for (const user of adminUsers) {
        if (!user.userRole || user.userRole.name !== 'Admin') {
          adminTestPassed = false;
          break;
        }
        
        const permissions = user.userRole.permissions.map(rp => rp.permission.name);
        const requiredPermissions = ['admin:access', 'leaders:manage', 'users:create:staff', 'users:manage:staff'];
        
        for (const required of requiredPermissions) {
          if (!permissions.includes(required)) {
            adminTestPassed = false;
            break;
          }
        }
      }
      
      if (adminTestPassed && adminUsers.length === 2) {
        testResults.rbac.passed++;
        testResults.rbac.tests.push('✅ Admin users have proper roles and permissions');
        console.log('   ✅ Admin users have proper roles and permissions');
      } else {
        testResults.rbac.failed++;
        testResults.rbac.tests.push('❌ Admin users missing roles or permissions');
        console.log('   ❌ Admin users missing roles or permissions');
      }
    } catch (error) {
      testResults.rbac.failed++;
      testResults.rbac.tests.push(`❌ Admin role test failed: ${error.message}`);
      console.log(`   ❌ Admin role test failed: ${error.message}`);
    }
    
    // Test 1.2: All users have roles assigned
    try {
      const usersWithoutRoles = await prisma.user.count({
        where: { roleId: null }
      });
      
      if (usersWithoutRoles === 0) {
        testResults.rbac.passed++;
        testResults.rbac.tests.push('✅ All users have roles assigned');
        console.log('   ✅ All users have roles assigned');
      } else {
        testResults.rbac.failed++;
        testResults.rbac.tests.push(`❌ ${usersWithoutRoles} users without roles`);
        console.log(`   ❌ ${usersWithoutRoles} users without roles`);
      }
    } catch (error) {
      testResults.rbac.failed++;
      testResults.rbac.tests.push(`❌ User role assignment test failed: ${error.message}`);
      console.log(`   ❌ User role assignment test failed: ${error.message}`);
    }
    
    // Test 1.3: No orphaned permissions
    try {
      const orphanedPermissions = await prisma.permission.count({
        where: {
          roles: {
            none: {}
          }
        }
      });
      
      if (orphanedPermissions === 0) {
        testResults.rbac.passed++;
        testResults.rbac.tests.push('✅ No orphaned permissions found');
        console.log('   ✅ No orphaned permissions found');
      } else {
        testResults.rbac.failed++;
        testResults.rbac.tests.push(`❌ ${orphanedPermissions} orphaned permissions`);
        console.log(`   ❌ ${orphanedPermissions} orphaned permissions`);
      }
    } catch (error) {
      testResults.rbac.failed++;
      testResults.rbac.tests.push(`❌ Orphaned permissions test failed: ${error.message}`);
      console.log(`   ❌ Orphaned permissions test failed: ${error.message}`);
    }

    // 2. Political Parties Tests
    console.log('\n🏛️  Testing Political Parties System...');
    console.log('=======================================');
    
    // Test 2.1: Political parties are properly seeded
    try {
      const totalParties = await prisma.politicalParty.count();
      const activeParties = await prisma.politicalParty.count({
        where: { isActive: true }
      });
      
      if (totalParties >= 20 && activeParties >= 15) {
        testResults.politicalParties.passed++;
        testResults.politicalParties.tests.push(`✅ Political parties properly seeded (${totalParties} total, ${activeParties} active)`);
        console.log(`   ✅ Political parties properly seeded (${totalParties} total, ${activeParties} active)`);
      } else {
        testResults.politicalParties.failed++;
        testResults.politicalParties.tests.push(`❌ Insufficient political parties (${totalParties} total, ${activeParties} active)`);
        console.log(`   ❌ Insufficient political parties (${totalParties} total, ${activeParties} active)`);
      }
    } catch (error) {
      testResults.politicalParties.failed++;
      testResults.politicalParties.tests.push(`❌ Political parties seeding test failed: ${error.message}`);
      console.log(`   ❌ Political parties seeding test failed: ${error.message}`);
    }
    
    // Test 2.2: Major parties are present
    try {
      const majorParties = ['APC', 'PDP', 'LP', 'NNPP', 'APGA'];
      const foundParties = await prisma.politicalParty.findMany({
        where: {
          abbreviation: { in: majorParties }
        },
        select: { abbreviation: true }
      });
      
      if (foundParties.length === majorParties.length) {
        testResults.politicalParties.passed++;
        testResults.politicalParties.tests.push('✅ All major political parties present');
        console.log('   ✅ All major political parties present');
      } else {
        const missing = majorParties.filter(party => 
          !foundParties.some(found => found.abbreviation === party)
        );
        testResults.politicalParties.failed++;
        testResults.politicalParties.tests.push(`❌ Missing major parties: ${missing.join(', ')}`);
        console.log(`   ❌ Missing major parties: ${missing.join(', ')}`);
      }
    } catch (error) {
      testResults.politicalParties.failed++;
      testResults.politicalParties.tests.push(`❌ Major parties test failed: ${error.message}`);
      console.log(`   ❌ Major parties test failed: ${error.message}`);
    }
    
    // Test 2.3: Leaders have valid party references
    try {
      const leadersWithParties = await prisma.leader.findMany({
        where: { party: { not: null } },
        select: { party: true }
      });
      
      const partyAbbreviations = await prisma.politicalParty.findMany({
        select: { abbreviation: true }
      });
      
      const validAbbreviations = new Set(partyAbbreviations.map(p => p.abbreviation));
      const invalidReferences = leadersWithParties.filter(leader => 
        !validAbbreviations.has(leader.party)
      );
      
      if (invalidReferences.length === 0) {
        testResults.politicalParties.passed++;
        testResults.politicalParties.tests.push('✅ All leader party references are valid');
        console.log('   ✅ All leader party references are valid');
      } else {
        testResults.politicalParties.failed++;
        testResults.politicalParties.tests.push(`❌ ${invalidReferences.length} invalid party references`);
        console.log(`   ❌ ${invalidReferences.length} invalid party references`);
      }
    } catch (error) {
      testResults.politicalParties.failed++;
      testResults.politicalParties.tests.push(`❌ Leader party references test failed: ${error.message}`);
      console.log(`   ❌ Leader party references test failed: ${error.message}`);
    }

    // 3. Admin Access Tests
    console.log('\n👤 Testing Admin Access...');
    console.log('==========================');
    
    // Test 3.1: Admin users can access admin routes
    try {
      const adminUser = await prisma.user.findFirst({
        where: { email: '<EMAIL>' },
        include: {
          userRole: {
            include: {
              permissions: {
                include: {
                  permission: true
                }
              }
            }
          }
        }
      });
      
      if (adminUser && adminUser.userRole) {
        const permissions = adminUser.userRole.permissions.map(rp => rp.permission.name);
        const adminRoutes = [
          { route: '/admin', required: ['admin:access'] },
          { route: '/admin/leaders', required: ['leaders:manage', 'admin:access'] },
          { route: '/admin/users', required: ['users:manage', 'admin:access'] },
          { route: '/admin/roles', required: ['admin:roles', 'admin:access'] }
        ];
        
        let allRoutesAccessible = true;
        for (const routeTest of adminRoutes) {
          const hasAccess = routeTest.required.some(permission => permissions.includes(permission));
          if (!hasAccess) {
            allRoutesAccessible = false;
            break;
          }
        }
        
        if (allRoutesAccessible) {
          testResults.adminAccess.passed++;
          testResults.adminAccess.tests.push('✅ Admin can access all admin routes');
          console.log('   ✅ Admin can access all admin routes');
        } else {
          testResults.adminAccess.failed++;
          testResults.adminAccess.tests.push('❌ Admin missing access to some routes');
          console.log('   ❌ Admin missing access to some routes');
        }
      } else {
        testResults.adminAccess.failed++;
        testResults.adminAccess.tests.push('❌ Admin user not found or has no role');
        console.log('   ❌ Admin user not found or has no role');
      }
    } catch (error) {
      testResults.adminAccess.failed++;
      testResults.adminAccess.tests.push(`❌ Admin access test failed: ${error.message}`);
      console.log(`   ❌ Admin access test failed: ${error.message}`);
    }
    
    // Test 3.2: Staff management permissions
    try {
      const adminUser = await prisma.user.findFirst({
        where: { email: '<EMAIL>' },
        include: {
          userRole: {
            include: {
              permissions: {
                include: {
                  permission: true
                }
              }
            }
          }
        }
      });
      
      if (adminUser && adminUser.userRole) {
        const permissions = adminUser.userRole.permissions.map(rp => rp.permission.name);
        const staffPermissions = ['users:create:staff', 'users:manage:staff'];
        const hasStaffPermissions = staffPermissions.every(permission => permissions.includes(permission));
        
        if (hasStaffPermissions) {
          testResults.adminAccess.passed++;
          testResults.adminAccess.tests.push('✅ Admin has staff management permissions');
          console.log('   ✅ Admin has staff management permissions');
        } else {
          testResults.adminAccess.failed++;
          testResults.adminAccess.tests.push('❌ Admin missing staff management permissions');
          console.log('   ❌ Admin missing staff management permissions');
        }
      } else {
        testResults.adminAccess.failed++;
        testResults.adminAccess.tests.push('❌ Cannot verify staff permissions - admin user not found');
        console.log('   ❌ Cannot verify staff permissions - admin user not found');
      }
    } catch (error) {
      testResults.adminAccess.failed++;
      testResults.adminAccess.tests.push(`❌ Staff permissions test failed: ${error.message}`);
      console.log(`   ❌ Staff permissions test failed: ${error.message}`);
    }

    // 4. Data Integrity Tests
    console.log('\n📊 Testing Data Integrity...');
    console.log('=============================');
    
    // Test 4.1: Database relationships are intact
    try {
      const usersWithRoles = await prisma.user.count({
        where: {
          userRole: {
            isNot: null
          }
        }
      });
      
      const totalUsers = await prisma.user.count();
      
      if (usersWithRoles === totalUsers) {
        testResults.dataIntegrity.passed++;
        testResults.dataIntegrity.tests.push('✅ All user-role relationships intact');
        console.log('   ✅ All user-role relationships intact');
      } else {
        testResults.dataIntegrity.failed++;
        testResults.dataIntegrity.tests.push(`❌ ${totalUsers - usersWithRoles} users with broken role relationships`);
        console.log(`   ❌ ${totalUsers - usersWithRoles} users with broken role relationships`);
      }
    } catch (error) {
      testResults.dataIntegrity.failed++;
      testResults.dataIntegrity.tests.push(`❌ Relationship integrity test failed: ${error.message}`);
      console.log(`   ❌ Relationship integrity test failed: ${error.message}`);
    }
    
    // Test 4.2: Audit logs are being created
    try {
      const recentAuditLogs = await prisma.auditLog.count({
        where: {
          timestamp: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        }
      });
      
      if (recentAuditLogs > 0) {
        testResults.dataIntegrity.passed++;
        testResults.dataIntegrity.tests.push(`✅ Audit logging is active (${recentAuditLogs} recent logs)`);
        console.log(`   ✅ Audit logging is active (${recentAuditLogs} recent logs)`);
      } else {
        testResults.dataIntegrity.failed++;
        testResults.dataIntegrity.tests.push('❌ No recent audit logs found');
        console.log('   ❌ No recent audit logs found');
      }
    } catch (error) {
      testResults.dataIntegrity.failed++;
      testResults.dataIntegrity.tests.push(`❌ Audit logging test failed: ${error.message}`);
      console.log(`   ❌ Audit logging test failed: ${error.message}`);
    }

    // Generate Test Report
    console.log('\n📋 System Integration Test Report');
    console.log('==================================');
    
    const totalTests = Object.values(testResults).reduce((sum, category) => sum + category.passed + category.failed, 0);
    const totalPassed = Object.values(testResults).reduce((sum, category) => sum + category.passed, 0);
    const totalFailed = Object.values(testResults).reduce((sum, category) => sum + category.failed, 0);
    const successRate = totalTests > 0 ? (totalPassed / totalTests * 100).toFixed(1) : 0;
    
    console.log(`\n🎯 Overall Results:`);
    console.log(`   ✅ Passed: ${totalPassed}/${totalTests} tests`);
    console.log(`   ❌ Failed: ${totalFailed}/${totalTests} tests`);
    console.log(`   📊 Success Rate: ${successRate}%`);
    
    console.log(`\n📊 Category Breakdown:`);
    Object.entries(testResults).forEach(([category, results]) => {
      const categoryTotal = results.passed + results.failed;
      const categoryRate = categoryTotal > 0 ? (results.passed / categoryTotal * 100).toFixed(1) : 0;
      console.log(`   ${category}: ${results.passed}/${categoryTotal} (${categoryRate}%)`);
    });
    
    // System Health Assessment
    console.log(`\n🏥 System Health Assessment:`);
    if (successRate >= 90) {
      console.log('🟢 Excellent - System is fully operational');
    } else if (successRate >= 75) {
      console.log('🟡 Good - Minor issues detected');
    } else if (successRate >= 50) {
      console.log('🟠 Fair - Several issues need attention');
    } else {
      console.log('🔴 Poor - Critical issues require immediate action');
    }
    
    // Detailed Test Results
    console.log(`\n📝 Detailed Test Results:`);
    Object.entries(testResults).forEach(([category, results]) => {
      console.log(`\n${category.toUpperCase()}:`);
      results.tests.forEach(test => console.log(`   ${test}`));
    });
    
    // Create comprehensive audit log
    const adminUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ]
      }
    });
    
    await prisma.auditLog.create({
      data: {
        userId: adminUser?.id,
        action: 'system_integration_test',
        resource: 'system',
        details: {
          message: 'Comprehensive system integration test completed',
          total_tests: totalTests,
          tests_passed: totalPassed,
          tests_failed: totalFailed,
          success_rate: Number(successRate),
          category_results: testResults,
          system_health: successRate >= 90 ? 'Excellent' : successRate >= 75 ? 'Good' : successRate >= 50 ? 'Fair' : 'Poor',
          timestamp: new Date().toISOString()
        },
        success: totalFailed === 0
      }
    });
    
    console.log('\n🎉 System integration test completed!');
    
    // Exit with appropriate code
    process.exit(totalFailed === 0 ? 0 : 1);
    
  } catch (error) {
    console.error('❌ System integration test failed:', error);
    
    // Log the error
    try {
      await prisma.auditLog.create({
        data: {
          action: 'system_integration_test_error',
          resource: 'system',
          details: {
            message: 'System integration test encountered an error',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
          },
          success: false
        }
      });
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }
    
    throw error;
  }
}

// Run the integration test
runSystemIntegrationTest()
  .catch((e) => {
    console.error('❌ Integration test failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });