#!/usr/bin/env node

/**
 * Political Parties API Test Script
 * 
 * Tests the political parties API endpoints to ensure proper functionality
 * and permission handling after the recent fixes.
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testPoliticalPartiesAPI() {
  console.log('🧪 Testing Political Parties API...\n');
  
  try {
    // Test 1: Verify permissions exist
    console.log('1️⃣  Verifying Political Parties Permissions:');
    console.log('============================================');
    
    const politicalPartiesPermissions = await prisma.permission.findMany({
      where: {
        name: { startsWith: 'political-parties:' }
      },
      orderBy: { name: 'asc' }
    });
    
    console.log(`📋 Found ${politicalPartiesPermissions.length} political parties permissions:`);
    politicalPartiesPermissions.forEach(permission => {
      console.log(`   ✅ ${permission.name} - ${permission.description}`);
    });
    
    // Test 2: Verify admin users have permissions
    console.log('\n2️⃣  Verifying Admin User Permissions:');
    console.log('=====================================');
    
    const adminUsers = await prisma.user.findMany({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ]
      },
      include: {
        userRole: {
          include: {
            permissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    });
    
    for (const user of adminUsers) {
      console.log(`\n👤 ${user.email}:`);
      console.log(`   Role: ${user.userRole?.name || 'No role'}`);
      
      if (user.userRole) {
        const userPermissions = user.userRole.permissions.map(rp => rp.permission.name);
        const politicalPartiesPerms = userPermissions.filter(p => p.startsWith('political-parties:'));
        
        console.log(`   Political Parties Permissions: ${politicalPartiesPerms.length}/5`);
        politicalPartiesPerms.forEach(permission => {
          console.log(`      ✅ ${permission}`);
        });
        
        // Check if user has all required permissions
        const requiredPermissions = [
          'political-parties:read',
          'political-parties:create', 
          'political-parties:update',
          'political-parties:delete',
          'political-parties:manage'
        ];
        
        const missingPermissions = requiredPermissions.filter(p => !politicalPartiesPerms.includes(p));
        if (missingPermissions.length > 0) {
          console.log(`   ❌ Missing Permissions: ${missingPermissions.join(', ')}`);
        } else {
          console.log(`   ✅ Has all required permissions`);
        }
      }
    }
    
    // Test 3: Verify database data
    console.log('\n3️⃣  Verifying Political Parties Data:');
    console.log('====================================');
    
    const totalParties = await prisma.politicalParty.count();
    const activeParties = await prisma.politicalParty.count({
      where: { isActive: true }
    });
    const inactiveParties = totalParties - activeParties;
    
    console.log(`📊 Total Parties: ${totalParties}`);
    console.log(`🟢 Active Parties: ${activeParties}`);
    console.log(`🔴 Inactive Parties: ${inactiveParties}`);
    
    // Show sample parties
    const sampleParties = await prisma.politicalParty.findMany({
      take: 5,
      orderBy: { name: 'asc' },
      select: {
        name: true,
        abbreviation: true,
        isActive: true,
        foundedYear: true
      }
    });
    
    console.log('\n📋 Sample Parties:');
    sampleParties.forEach(party => {
      const status = party.isActive ? '🟢' : '🔴';
      console.log(`   ${status} ${party.abbreviation} - ${party.name} (${party.foundedYear || 'N/A'})`);
    });
    
    // Test 4: Verify API endpoint structure
    console.log('\n4️⃣  API Endpoint Structure Verification:');
    console.log('========================================');
    
    const apiEndpoints = [
      {
        path: '/api/admin/political-parties',
        methods: ['GET', 'POST'],
        permissions: ['political-parties:read', 'political-parties:create', 'admin:access'],
        description: 'Main political parties API'
      },
      {
        path: '/api/admin/political-parties/[id]',
        methods: ['GET', 'PUT', 'DELETE'],
        permissions: ['political-parties:read', 'political-parties:update', 'political-parties:delete', 'admin:access'],
        description: 'Individual party management'
      },
      {
        path: '/api/admin/political-parties/analytics',
        methods: ['GET'],
        permissions: ['admin:access', 'analytics:view'],
        description: 'Political parties analytics'
      }
    ];
    
    console.log('📍 API Endpoints:');
    apiEndpoints.forEach(endpoint => {
      console.log(`   ${endpoint.path}`);
      console.log(`      Methods: ${endpoint.methods.join(', ')}`);
      console.log(`      Permissions: ${endpoint.permissions.join(' OR ')}`);
      console.log(`      Description: ${endpoint.description}`);
      console.log('');
    });
    
    // Test 5: Permission matrix verification
    console.log('5️⃣  Permission Matrix Verification:');
    console.log('===================================');
    
    const roles = await prisma.role.findMany({
      where: {
        name: { in: ['User', 'Content Creator', 'Moderator', 'Editor', 'Manager', 'Admin'] }
      },
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      }
    });
    
    console.log('🔐 Role-Permission Matrix for Political Parties:');
    console.log('Role'.padEnd(15) + ' | ' + 'Read | Create | Update | Delete | Manage');
    console.log('-'.repeat(60));
    
    for (const role of roles) {
      const permissions = role.permissions.map(rp => rp.permission.name);
      const hasRead = permissions.includes('political-parties:read') ? '✅' : '❌';
      const hasCreate = permissions.includes('political-parties:create') ? '✅' : '❌';
      const hasUpdate = permissions.includes('political-parties:update') ? '✅' : '❌';
      const hasDelete = permissions.includes('political-parties:delete') ? '✅' : '❌';
      const hasManage = permissions.includes('political-parties:manage') ? '✅' : '❌';
      
      console.log(
        role.name.padEnd(15) + ' | ' +
        hasRead.padEnd(4) + ' | ' +
        hasCreate.padEnd(6) + ' | ' +
        hasUpdate.padEnd(6) + ' | ' +
        hasDelete.padEnd(6) + ' | ' +
        hasManage
      );
    }
    
    // Test 6: Generate test summary
    console.log('\n6️⃣  Test Summary:');
    console.log('=================');
    
    const testResults = {
      permissionsExist: politicalPartiesPermissions.length === 5,
      adminHasPermissions: adminUsers.every(user => {
        const userPermissions = user.userRole?.permissions.map(rp => rp.permission.name) || [];
        const requiredPermissions = [
          'political-parties:read',
          'political-parties:create', 
          'political-parties:update',
          'political-parties:delete',
          'political-parties:manage'
        ];
        return requiredPermissions.every(p => userPermissions.includes(p));
      }),
      dataExists: totalParties > 0,
      rolesConfigured: roles.length === 6
    };
    
    const allTestsPassed = Object.values(testResults).every(result => result === true);
    
    console.log(`✅ Permissions Created: ${testResults.permissionsExist ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Admin Permissions: ${testResults.adminHasPermissions ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Data Available: ${testResults.dataExists ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Roles Configured: ${testResults.rolesConfigured ? 'PASS' : 'FAIL'}`);
    
    console.log(`\n🎯 Overall Status: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    
    // Test 7: Recommendations
    console.log('\n💡 Recommendations:');
    console.log('===================');
    
    if (allTestsPassed) {
      console.log('🟢 System is ready for use!');
      console.log('   • Political parties API should now work correctly');
      console.log('   • Admin users can access /admin/political-parties');
      console.log('   • All CRUD operations are properly protected');
      console.log('   • Analytics endpoint is available for insights');
    } else {
      console.log('🟡 Issues found that need attention:');
      
      if (!testResults.permissionsExist) {
        console.log('   • Run: npm run rbac:add-political-parties-permissions');
      }
      
      if (!testResults.adminHasPermissions) {
        console.log('   • Verify admin role assignments');
        console.log('   • Run: npm run rbac:optimize');
      }
      
      if (!testResults.dataExists) {
        console.log('   • Run: npm run seed:political-parties');
      }
      
      if (!testResults.rolesConfigured) {
        console.log('   • Run: npm run rbac:optimize');
      }
    }
    
    // Create audit log
    const adminUser = adminUsers[0];
    await prisma.auditLog.create({
      data: {
        userId: adminUser?.id,
        action: 'political_parties_api_tested',
        resource: 'api_endpoints',
        details: {
          message: 'Political parties API comprehensive test completed',
          permissions_found: politicalPartiesPermissions.length,
          total_parties: totalParties,
          active_parties: activeParties,
          admin_users_tested: adminUsers.length,
          all_tests_passed: allTestsPassed,
          test_results: testResults,
          timestamp: new Date().toISOString()
        },
        success: allTestsPassed
      }
    });
    
    console.log('\n🎉 Political parties API test completed!');
    
  } catch (error) {
    console.error('❌ API test failed:', error);
    throw error;
  }
}

// Run the test
testPoliticalPartiesAPI()
  .catch((e) => {
    console.error('❌ Test failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });