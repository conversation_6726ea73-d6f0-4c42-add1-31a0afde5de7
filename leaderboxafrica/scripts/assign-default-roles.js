#!/usr/bin/env node

/**
 * Assign Default Roles Script
 * 
 * Assigns appropriate default roles to users who don't have roles assigned.
 * This is a quick fix for the common issue of users without proper RBAC setup.
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function assignDefaultRoles() {
  console.log('🔧 Assigning default roles to users...');
  
  try {
    // Get all users without roles
    const usersWithoutRoles = await prisma.user.findMany({
      where: {
        OR: [
          { roleId: null },
          { userRole: null }
        ]
      },
      include: {
        userRole: true
      }
    });
    
    if (usersWithoutRoles.length === 0) {
      console.log('✅ All users already have roles assigned!');
      return;
    }
    
    console.log(`Found ${usersWithoutRoles.length} users without roles`);
    
    // Get default roles
    const userRole = await prisma.role.findUnique({ where: { name: 'User' } });
    const adminRole = await prisma.role.findUnique({ where: { name: 'Admin' } });
    
    if (!userRole) {
      throw new Error('Default "User" role not found. Please run the optimization script first.');
    }
    
    if (!adminRole) {
      throw new Error('Default "Admin" role not found. Please run the optimization script first.');
    }
    
    let assignedUsers = 0;
    let assignedAdmins = 0;
    
    for (const user of usersWithoutRoles) {
      // Determine appropriate role
      const isAdmin = user.isAdmin || 
                     user.email === '<EMAIL>' || 
                     user.email === '<EMAIL>';
      
      const targetRole = isAdmin ? adminRole : userRole;
      
      // Update user role
      await prisma.user.update({
        where: { id: user.id },
        data: { roleId: targetRole.id }
      });
      
      // Create UserRole record for audit trail
      await prisma.userRole.upsert({
        where: {
          userId_roleId_isActive: {
            userId: user.id,
            roleId: targetRole.id,
            isActive: true
          }
        },
        update: {},
        create: {
          userId: user.id,
          roleId: targetRole.id,
          assignedBy: user.id, // Self-assigned for system fix
          isActive: true
        }
      });
      
      if (isAdmin) {
        assignedAdmins++;
        console.log(`✅ Assigned Admin role to ${user.email}`);
      } else {
        assignedUsers++;
        console.log(`✅ Assigned User role to ${user.email}`);
      }
    }
    
    // Create audit log
    await prisma.auditLog.create({
      data: {
        action: 'bulk_role_assignment',
        resource: 'users',
        details: {
          message: 'Assigned default roles to users without roles',
          users_assigned: assignedUsers,
          admins_assigned: assignedAdmins,
          total_assigned: assignedUsers + assignedAdmins,
          timestamp: new Date().toISOString()
        },
        success: true
      }
    });
    
    console.log(`\n🎉 Successfully assigned roles:`);
    console.log(`   • ${assignedUsers} users assigned "User" role`);
    console.log(`   • ${assignedAdmins} users assigned "Admin" role`);
    console.log(`   • Total: ${assignedUsers + assignedAdmins} users updated`);
    
  } catch (error) {
    console.error('❌ Failed to assign default roles:', error);
    throw error;
  }
}

// Run the script
assignDefaultRoles()
  .catch((e) => {
    console.error('❌ Script failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });