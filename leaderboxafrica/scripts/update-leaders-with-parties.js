#!/usr/bin/env node

/**
 * Update Leaders with Political Party References
 * 
 * Updates existing leader profiles to reference the proper political parties
 * from the political_parties table instead of using string values.
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Mapping of party abbreviations/names to standardize references
const PARTY_MAPPINGS = {
  'APC': 'APC',
  'All Progressives Congress': 'APC',
  'PDP': 'PDP',
  'Peoples Democratic Party': 'PDP',
  'LP': 'LP',
  'Labour Party': 'LP',
  'NNPP': 'NNPP',
  'New Nigeria Peoples Party': 'NNPP',
  'APGA': 'APGA',
  'All Progressives Grand Alliance': 'APGA',
  'SDP': 'SDP',
  'Social Democratic Party': 'SDP',
  'YPP': 'YPP',
  'Young Progressive Party': 'YPP',
  'AAC': 'AAC',
  'African Action Congress': 'AAC',
  'AP': 'AP',
  'Accord Party': 'AP',
  'AA': 'AA',
  'Action Alliance': 'AA'
};

async function updateLeadersWithParties() {
  console.log('👥 Updating Leaders with Political Party References...\n');
  
  try {
    // Get all political parties for reference
    const politicalParties = await prisma.politicalParty.findMany({
      select: {
        id: true,
        name: true,
        abbreviation: true,
        isActive: true
      }
    });
    
    console.log(`📋 Found ${politicalParties.length} political parties in database`);
    
    // Create a lookup map for parties
    const partyLookup = new Map();
    politicalParties.forEach(party => {
      partyLookup.set(party.abbreviation.toUpperCase(), party);
      partyLookup.set(party.name.toUpperCase(), party);
    });
    
    // Get all leaders with their current party information
    const leaders = await prisma.leader.findMany({
      select: {
        id: true,
        name: true,
        party: true,
        position: true
      }
    });
    
    console.log(`👤 Found ${leaders.length} leaders to process\n`);
    
    let updatedCount = 0;
    let skippedCount = 0;
    let unmatchedParties = new Set();
    
    for (const leader of leaders) {
      if (!leader.party) {
        skippedCount++;
        console.log(`   ⚠️  Skipped: ${leader.name} - No party information`);
        continue;
      }
      
      // Normalize party name for lookup
      const normalizedParty = leader.party.trim().toUpperCase();
      let matchedParty = null;
      
      // Try direct lookup first
      if (partyLookup.has(normalizedParty)) {
        matchedParty = partyLookup.get(normalizedParty);
      } else {
        // Try mapping lookup
        const mappedAbbreviation = PARTY_MAPPINGS[leader.party];
        if (mappedAbbreviation && partyLookup.has(mappedAbbreviation)) {
          matchedParty = partyLookup.get(mappedAbbreviation);
        }
      }
      
      if (matchedParty) {
        // Update leader with standardized party abbreviation
        await prisma.leader.update({
          where: { id: leader.id },
          data: {
            party: matchedParty.abbreviation
          }
        });
        
        updatedCount++;
        console.log(`   ✅ Updated: ${leader.name} - ${leader.party} → ${matchedParty.abbreviation}`);
      } else {
        skippedCount++;
        unmatchedParties.add(leader.party);
        console.log(`   ❌ No match: ${leader.name} - Party: "${leader.party}"`);
      }
    }
    
    // Get admin user for audit log
    const adminUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ]
      }
    });
    
    // Create audit log
    await prisma.auditLog.create({
      data: {
        userId: adminUser?.id,
        action: 'leaders_party_update',
        resource: 'leaders',
        details: {
          message: 'Updated leader party references to use standardized abbreviations',
          leaders_updated: updatedCount,
          leaders_skipped: skippedCount,
          total_leaders: leaders.length,
          unmatched_parties: Array.from(unmatchedParties),
          timestamp: new Date().toISOString()
        },
        success: true
      }
    });
    
    console.log('\n📊 Update Summary:');
    console.log('==================');
    console.log(`✅ Updated: ${updatedCount} leaders`);
    console.log(`⚠️  Skipped: ${skippedCount} leaders`);
    console.log(`📈 Total Processed: ${leaders.length} leaders`);
    
    if (unmatchedParties.size > 0) {
      console.log('\n❓ Unmatched Parties:');
      Array.from(unmatchedParties).forEach(party => {
        console.log(`   • "${party}"`);
      });
      
      console.log('\n💡 Suggestions:');
      console.log('   • Add these parties to the political_parties table');
      console.log('   • Update the PARTY_MAPPINGS in this script');
      console.log('   • Manually correct leader party information');
    }
    
    // Show party distribution
    console.log('\n🏛️  Party Distribution:');
    const partyStats = await prisma.leader.groupBy({
      by: ['party'],
      _count: {
        party: true
      },
      where: {
        party: {
          not: null
        }
      },
      orderBy: {
        _count: {
          party: 'desc'
        }
      }
    });
    
    for (const stat of partyStats) {
      const partyInfo = partyLookup.get(stat.party?.toUpperCase());
      const partyName = partyInfo ? partyInfo.name : stat.party;
      console.log(`   • ${stat.party}: ${stat._count.party} leaders (${partyName})`);
    }
    
    console.log('\n🎉 Leader party references updated successfully!');
    
  } catch (error) {
    console.error('❌ Update failed:', error);
    throw error;
  }
}

// Run the update
updateLeadersWithParties()
  .catch((e) => {
    console.error('❌ Update failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });