#!/usr/bin/env node

/**
 * Add Political Parties Permissions Script
 * 
 * Adds missing political parties permissions to the RBAC system
 * and assigns them to appropriate roles.
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Political parties permissions to add
const POLITICAL_PARTIES_PERMISSIONS = [
  {
    name: 'political-parties:read',
    resource: 'political-parties',
    action: 'read',
    description: 'View political parties'
  },
  {
    name: 'political-parties:create',
    resource: 'political-parties',
    action: 'create',
    description: 'Create new political parties'
  },
  {
    name: 'political-parties:update',
    resource: 'political-parties',
    action: 'update',
    description: 'Update political party information'
  },
  {
    name: 'political-parties:delete',
    resource: 'political-parties',
    action: 'delete',
    description: 'Delete political parties'
  },
  {
    name: 'political-parties:manage',
    resource: 'political-parties',
    action: 'manage',
    description: 'Full political parties management access'
  }
];

async function addPoliticalPartiesPermissions() {
  console.log('🏛️  Adding Political Parties Permissions...\n');
  
  try {
    // Step 1: Create the permissions
    console.log('📋 Creating political parties permissions...');
    
    const createdPermissions = [];
    
    for (const permissionData of POLITICAL_PARTIES_PERMISSIONS) {
      const permission = await prisma.permission.upsert({
        where: { name: permissionData.name },
        update: { description: permissionData.description },
        create: permissionData
      });
      
      createdPermissions.push(permission);
      console.log(`   ✅ ${permissionData.name} - ${permissionData.description}`);
    }
    
    console.log(`\n✅ Created/updated ${createdPermissions.length} permissions`);
    
    // Step 2: Assign permissions to roles
    console.log('\n👥 Assigning permissions to roles...');
    
    // Get roles
    const roles = await prisma.role.findMany({
      where: {
        name: { in: ['User', 'Content Creator', 'Moderator', 'Editor', 'Manager', 'Admin'] }
      }
    });
    
    const roleMap = new Map();
    roles.forEach(role => roleMap.set(role.name, role));
    
    // Define role-permission assignments
    const rolePermissions = {
      'User': ['political-parties:read'],
      'Content Creator': ['political-parties:read'],
      'Moderator': ['political-parties:read'],
      'Editor': ['political-parties:read', 'political-parties:create', 'political-parties:update'],
      'Manager': ['political-parties:read', 'political-parties:create', 'political-parties:update', 'political-parties:delete', 'political-parties:manage'],
      'Admin': ['political-parties:read', 'political-parties:create', 'political-parties:update', 'political-parties:delete', 'political-parties:manage']
    };
    
    let totalAssignments = 0;
    
    for (const [roleName, permissionNames] of Object.entries(rolePermissions)) {
      const role = roleMap.get(roleName);
      if (!role) {
        console.log(`   ⚠️  Role ${roleName} not found, skipping...`);
        continue;
      }
      
      console.log(`\n   🛡️  Assigning permissions to ${roleName}:`);
      
      for (const permissionName of permissionNames) {
        const permission = createdPermissions.find(p => p.name === permissionName);
        if (!permission) continue;
        
        // Check if assignment already exists
        const existingAssignment = await prisma.rolePermission.findUnique({
          where: {
            roleId_permissionId: {
              roleId: role.id,
              permissionId: permission.id
            }
          }
        });
        
        if (existingAssignment) {
          console.log(`      ✅ ${permissionName} (already assigned)`);
          continue;
        }
        
        // Create new assignment
        await prisma.rolePermission.create({
          data: {
            roleId: role.id,
            permissionId: permission.id
          }
        });
        
        totalAssignments++;
        console.log(`      ✅ ${permissionName} (newly assigned)`);
      }
    }
    
    console.log(`\n✅ Created ${totalAssignments} new role-permission assignments`);
    
    // Step 3: Verify admin users have access
    console.log('\n👤 Verifying admin user access...');
    
    const adminUsers = await prisma.user.findMany({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ]
      },
      include: {
        userRole: {
          include: {
            permissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    });
    
    for (const user of adminUsers) {
      console.log(`\n   👤 ${user.email}:`);
      console.log(`      Role: ${user.userRole?.name || 'No role'}`);
      
      if (user.userRole) {
        const userPermissions = user.userRole.permissions.map(rp => rp.permission.name);
        const politicalPartiesPermissions = userPermissions.filter(p => p.startsWith('political-parties:'));
        
        console.log(`      Political Parties Permissions: ${politicalPartiesPermissions.length}`);
        politicalPartiesPermissions.forEach(permission => {
          console.log(`         ✅ ${permission}`);
        });
      }
    }
    
    // Step 4: Create audit log
    const adminUser = adminUsers.find(u => u.email === '<EMAIL>') || adminUsers[0];
    
    await prisma.auditLog.create({
      data: {
        userId: adminUser?.id,
        action: 'political_parties_permissions_added',
        resource: 'permissions',
        details: {
          message: 'Added political parties permissions to RBAC system',
          permissions_created: createdPermissions.length,
          role_assignments_created: totalAssignments,
          permissions: POLITICAL_PARTIES_PERMISSIONS.map(p => p.name),
          timestamp: new Date().toISOString()
        },
        success: true
      }
    });
    
    console.log('\n📊 Summary:');
    console.log('============');
    console.log(`✅ Permissions Created/Updated: ${createdPermissions.length}`);
    console.log(`✅ Role Assignments Created: ${totalAssignments}`);
    console.log(`✅ Admin Users Verified: ${adminUsers.length}`);
    
    console.log('\n🎉 Political parties permissions added successfully!');
    console.log('\n💡 Next Steps:');
    console.log('   • Test the /admin/political-parties page');
    console.log('   • Verify API endpoints are accessible');
    console.log('   • Run comprehensive API permission tests');
    
  } catch (error) {
    console.error('❌ Failed to add political parties permissions:', error);
    throw error;
  }
}

// Run the script
addPoliticalPartiesPermissions()
  .catch((e) => {
    console.error('❌ Script failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });