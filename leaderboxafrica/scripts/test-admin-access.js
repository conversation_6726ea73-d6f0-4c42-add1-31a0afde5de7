#!/usr/bin/env node

/**
 * Test Admin Access Script
 * 
 * Tests if admin users have the correct permissions to access admin routes
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testAdminAccess() {
  console.log('🧪 Testing Admin Access...\n');
  
  try {
    // Get admin users
    const adminUsers = await prisma.user.findMany({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ]
      },
      include: {
        userRole: {
          include: {
            permissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    });
    
    console.log(`Found ${adminUsers.length} admin users to test:\n`);
    
    for (const user of adminUsers) {
      console.log(`👤 Testing user: ${user.email}`);
      console.log(`   Role: ${user.userRole?.name || 'No role assigned'}`);
      
      if (!user.userRole) {
        console.log('   ❌ No role assigned - this user cannot access admin routes');
        continue;
      }
      
      // Get user permissions
      const permissions = user.userRole.permissions.map(rp => rp.permission.name);
      console.log(`   Permissions: ${permissions.length} total`);
      
      // Test specific route permissions
      const routeTests = [
        { route: '/admin', required: ['admin:access'] },
        { route: '/admin/leaders', required: ['leaders:manage', 'admin:access'] },
        { route: '/admin/users', required: ['users:manage', 'admin:access'] },
        { route: '/admin/roles', required: ['admin:roles', 'admin:access'] }
      ];
      
      console.log('   Route Access Tests:');
      
      for (const test of routeTests) {
        // Check if user has ANY of the required permissions (OR logic)
        const hasAccess = test.required.some(permission => permissions.includes(permission));
        
        if (hasAccess) {
          console.log(`   ✅ ${test.route} - Access granted`);
        } else {
          console.log(`   ❌ ${test.route} - Access denied (missing: ${test.required.join(' OR ')})`);
        }
      }
      
      console.log(''); // Empty line for readability
    }
    
    // Test the specific issue: /admin/leaders access
    console.log('🎯 Specific Test: /admin/leaders access');
    console.log('Required permissions: leaders:manage OR admin:access');
    
    for (const user of adminUsers) {
      if (!user.userRole) continue;
      
      const permissions = user.userRole.permissions.map(rp => rp.permission.name);
      const hasLeadersManage = permissions.includes('leaders:manage');
      const hasAdminAccess = permissions.includes('admin:access');
      
      console.log(`\n${user.email}:`);
      console.log(`   Has 'leaders:manage': ${hasLeadersManage}`);
      console.log(`   Has 'admin:access': ${hasAdminAccess}`);
      console.log(`   Can access /admin/leaders: ${hasLeadersManage || hasAdminAccess ? '✅ YES' : '❌ NO'}`);
    }
    
    console.log('\n✅ Admin access test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run the test
testAdminAccess()
  .catch((e) => {
    console.error('❌ Test failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });