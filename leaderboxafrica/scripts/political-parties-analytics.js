#!/usr/bin/env node

/**
 * Political Parties Analytics Script
 * 
 * Generates comprehensive analytics and insights about political parties,
 * their representation, leader distribution, and engagement metrics.
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function generatePoliticalPartiesAnalytics() {
  console.log('📊 Generating Political Parties Analytics...\n');
  
  try {
    // 1. Basic Party Statistics
    console.log('🏛️  Basic Party Statistics:');
    console.log('============================');
    
    const totalParties = await prisma.politicalParty.count();
    const activeParties = await prisma.politicalParty.count({
      where: { isActive: true }
    });
    const inactiveParties = totalParties - activeParties;
    
    console.log(`📈 Total Parties: ${totalParties}`);
    console.log(`🟢 Active Parties: ${activeParties}`);
    console.log(`🔴 Inactive Parties: ${inactiveParties}`);
    console.log(`📊 Active Rate: ${((activeParties / totalParties) * 100).toFixed(1)}%`);
    
    // 2. Party Age Analysis
    console.log('\n📅 Party Age Analysis:');
    console.log('======================');
    
    const partiesWithFoundedYear = await prisma.politicalParty.findMany({
      where: {
        foundedYear: { not: null },
        isActive: true
      },
      select: {
        name: true,
        abbreviation: true,
        foundedYear: true
      },
      orderBy: { foundedYear: 'asc' }
    });
    
    const currentYear = new Date().getFullYear();
    let ageGroups = {
      'Legacy (Before 1999)': 0,
      'Fourth Republic Era (1999-2010)': 0,
      'Modern Era (2011-2020)': 0,
      'Recent (2021+)': 0
    };
    
    console.log('\n🕰️  Oldest Active Parties:');
    partiesWithFoundedYear.slice(0, 5).forEach(party => {
      const age = currentYear - party.foundedYear;
      console.log(`   • ${party.name} (${party.abbreviation}) - ${party.foundedYear} (${age} years old)`);
      
      // Categorize by age
      if (party.foundedYear < 1999) {
        ageGroups['Legacy (Before 1999)']++;
      } else if (party.foundedYear <= 2010) {
        ageGroups['Fourth Republic Era (1999-2010)']++;
      } else if (party.foundedYear <= 2020) {
        ageGroups['Modern Era (2011-2020)']++;
      } else {
        ageGroups['Recent (2021+)']++;
      }
    });
    
    console.log('\n📊 Party Age Distribution:');
    Object.entries(ageGroups).forEach(([group, count]) => {
      console.log(`   • ${group}: ${count} parties`);
    });
    
    // 3. Leader Distribution by Party
    console.log('\n👥 Leader Distribution by Party:');
    console.log('=================================');
    
    const leadersByParty = await prisma.leader.groupBy({
      by: ['party'],
      _count: {
        party: true
      },
      where: {
        party: { not: null }
      },
      orderBy: {
        _count: {
          party: 'desc'
        }
      }
    });
    
    // Get party details for better display
    const partyDetails = await prisma.politicalParty.findMany({
      select: {
        abbreviation: true,
        name: true,
        isActive: true
      }
    });
    
    const partyLookup = new Map();
    partyDetails.forEach(party => {
      partyLookup.set(party.abbreviation, party);
    });
    
    console.log('\n🏆 Top Parties by Leader Count:');
    leadersByParty.slice(0, 10).forEach((stat, index) => {
      const party = partyLookup.get(stat.party);
      const partyName = party ? party.name : 'Unknown Party';
      const status = party?.isActive ? '🟢' : '🔴';
      console.log(`   ${index + 1}. ${status} ${stat.party}: ${stat._count.party} leaders (${partyName})`);
    });
    
    // 4. Party Representation Analysis
    console.log('\n🗺️  Party Representation Analysis:');
    console.log('===================================');
    
    const totalLeaders = await prisma.leader.count({
      where: { party: { not: null } }
    });
    
    console.log(`📊 Total Leaders with Party Affiliation: ${totalLeaders}`);
    
    // Calculate representation percentages
    console.log('\n📈 Party Representation Percentages:');
    leadersByParty.forEach(stat => {
      const percentage = ((stat._count.party / totalLeaders) * 100).toFixed(1);
      const party = partyLookup.get(stat.party);
      const status = party?.isActive ? '🟢' : '🔴';
      console.log(`   ${status} ${stat.party}: ${percentage}% (${stat._count.party}/${totalLeaders})`);
    });
    
    // 5. Party Activity and Engagement
    console.log('\n📱 Party Activity and Engagement:');
    console.log('==================================');
    
    // Get leaders with ratings and followers for each party
    const partyEngagement = await prisma.leader.groupBy({
      by: ['party'],
      _avg: {
        ratingAverage: true,
        followersCount: true,
        ratingCount: true
      },
      _sum: {
        followersCount: true,
        ratingCount: true
      },
      where: {
        party: { not: null }
      },
      orderBy: {
        _sum: {
          followersCount: 'desc'
        }
      }
    });
    
    console.log('\n🌟 Party Engagement Metrics:');
    partyEngagement.slice(0, 8).forEach(stat => {
      const party = partyLookup.get(stat.party);
      const status = party?.isActive ? '🟢' : '🔴';
      console.log(`   ${status} ${stat.party}:`);
      console.log(`      👥 Total Followers: ${stat._sum.followersCount?.toLocaleString() || 0}`);
      console.log(`      ⭐ Avg Rating: ${stat._avg.ratingAverage ? Number(stat._avg.ratingAverage).toFixed(2) : 'N/A'}`);
      console.log(`      📊 Total Ratings: ${stat._sum.ratingCount?.toLocaleString() || 0}`);
      console.log(`      📈 Avg Followers per Leader: ${stat._avg.followersCount ? Math.round(stat._avg.followersCount) : 0}`);
    });
    
    // 6. Geographic Distribution
    console.log('\n🗺️  Geographic Distribution:');
    console.log('=============================');
    
    const leadersByState = await prisma.leader.groupBy({
      by: ['state', 'party'],
      _count: {
        id: true
      },
      where: {
        state: { not: null },
        party: { not: null }
      },
      orderBy: [
        { state: 'asc' },
        { _count: { id: 'desc' } }
      ]
    });
    
    // Group by state
    const stateDistribution = new Map();
    leadersByState.forEach(stat => {
      if (!stateDistribution.has(stat.state)) {
        stateDistribution.set(stat.state, []);
      }
      stateDistribution.get(stat.state).push({
        party: stat.party,
        count: stat._count.id
      });
    });
    
    console.log('\n🏛️  Top States by Political Representation:');
    const sortedStates = Array.from(stateDistribution.entries())
      .map(([state, parties]) => ({
        state,
        totalLeaders: parties.reduce((sum, p) => sum + p.count, 0),
        parties
      }))
      .sort((a, b) => b.totalLeaders - a.totalLeaders)
      .slice(0, 10);
    
    sortedStates.forEach(({ state, totalLeaders, parties }) => {
      console.log(`   📍 ${state}: ${totalLeaders} leaders`);
      parties.slice(0, 3).forEach(party => {
        const partyInfo = partyLookup.get(party.party);
        const status = partyInfo?.isActive ? '🟢' : '🔴';
        console.log(`      ${status} ${party.party}: ${party.count}`);
      });
    });
    
    // 7. Party Ideology Analysis
    console.log('\n💭 Party Ideology Analysis:');
    console.log('============================');
    
    const ideologyGroups = await prisma.politicalParty.groupBy({
      by: ['ideology'],
      _count: {
        ideology: true
      },
      where: {
        ideology: { not: null },
        isActive: true
      },
      orderBy: {
        _count: {
          ideology: 'desc'
        }
      }
    });
    
    console.log('\n🎯 Ideological Distribution:');
    ideologyGroups.forEach(group => {
      console.log(`   • ${group.ideology}: ${group._count.ideology} parties`);
    });
    
    // 8. Historical Timeline
    console.log('\n📜 Historical Timeline:');
    console.log('========================');
    
    const timeline = await prisma.politicalParty.findMany({
      where: {
        foundedYear: { not: null }
      },
      select: {
        name: true,
        abbreviation: true,
        foundedYear: true,
        isActive: true
      },
      orderBy: { foundedYear: 'asc' }
    });
    
    // Group by decades
    const decades = new Map();
    timeline.forEach(party => {
      const decade = Math.floor(party.foundedYear / 10) * 10;
      if (!decades.has(decade)) {
        decades.set(decade, []);
      }
      decades.get(decade).push(party);
    });
    
    console.log('\n🕰️  Party Formation by Decade:');
    Array.from(decades.entries())
      .sort(([a], [b]) => a - b)
      .forEach(([decade, parties]) => {
        console.log(`   📅 ${decade}s: ${parties.length} parties`);
        parties.slice(0, 3).forEach(party => {
          const status = party.isActive ? '🟢' : '🔴';
          console.log(`      ${status} ${party.abbreviation} (${party.foundedYear})`);
        });
      });
    
    // 9. Generate Summary Report
    console.log('\n📋 Executive Summary:');
    console.log('=====================');
    
    const majorParties = leadersByParty.slice(0, 3);
    const dominantParty = majorParties[0];
    const dominantPartyInfo = partyLookup.get(dominantParty.party);
    
    console.log(`🏆 Most Represented Party: ${dominantParty.party} (${dominantPartyInfo?.name})`);
    console.log(`   📊 ${dominantParty._count.party} leaders (${((dominantParty._count.party / totalLeaders) * 100).toFixed(1)}% of total)`);
    
    const avgAge = partiesWithFoundedYear.reduce((sum, p) => sum + (currentYear - p.foundedYear), 0) / partiesWithFoundedYear.length;
    console.log(`📅 Average Party Age: ${Math.round(avgAge)} years`);
    
    const totalFollowers = partyEngagement.reduce((sum, p) => sum + (p._sum.followersCount || 0), 0);
    console.log(`👥 Total Political Followers: ${totalFollowers.toLocaleString()}`);
    
    const avgRating = partyEngagement.reduce((sum, p, _, arr) => sum + (Number(p._avg.ratingAverage) || 0), 0) / partyEngagement.length;
    console.log(`⭐ Average Party Rating: ${avgRating.toFixed(2)}/5.0`);
    
    // Create comprehensive audit log
    const adminUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { email: '<EMAIL>' }
        ]
      }
    });
    
    await prisma.auditLog.create({
      data: {
        userId: adminUser?.id,
        action: 'political_parties_analytics',
        resource: 'political_parties',
        details: {
          message: 'Generated comprehensive political parties analytics',
          total_parties: totalParties,
          active_parties: activeParties,
          total_leaders: totalLeaders,
          dominant_party: dominantParty.party,
          avg_party_age: Math.round(avgAge),
          total_followers: totalFollowers,
          avg_rating: Number(avgRating.toFixed(2)),
          top_parties: majorParties.map(p => ({
            party: p.party,
            leaders: p._count.party,
            percentage: Number(((p._count.party / totalLeaders) * 100).toFixed(1))
          })),
          timestamp: new Date().toISOString()
        },
        success: true
      }
    });
    
    console.log('\n🎉 Political parties analytics completed successfully!');
    
  } catch (error) {
    console.error('❌ Analytics generation failed:', error);
    throw error;
  }
}

// Run the analytics
generatePoliticalPartiesAnalytics()
  .catch((e) => {
    console.error('❌ Analytics failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });