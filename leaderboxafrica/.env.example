# LeaderBox Environment Variables
# Copy this file to .env and fill in your actual values

# Database Configuration (Required)
# Replace with your actual PostgreSQL connection string
DATABASE_URL="postgresql://username:password@localhost:5432/leaderbox_dev"
DIRECT_URL="postgresql://username:password@localhost:5432/leaderbox_dev"

# Redis Configuration (Optional - for caching and sessions)
REDIS_URL="redis://:password@localhost:6379"

# Application Configuration
# Set to your domain in production
ORIGIN="http://localhost:5173"
NODE_ENV="development"
PORT="3000"
HOST="0.0.0.0"

# Zeptomail Configuration (Required for email functionality)
# Get these from your Zeptomail dashboard: https://www.zoho.com/zeptomail/
ZEPTOMAIL_API_KEY="your-zeptomail-api-key"
ZEPTOMAIL_FROM_EMAIL="<EMAIL>"
ZEPTOMAIL_FROM_NAME="LeaderBox"

# Security Configuration (Optional but recommended)
# Generate a secure random string for session encryption
SESSION_SECRET="your-32-character-or-longer-secret-key"

# Application URL (Optional)
# Public URL of your application for email links
APP_URL="https://yourdomain.com"

# File Upload Configuration
MAX_FILE_SIZE="10485760"  # 10MB in bytes
UPLOAD_DIR="uploads"

# AWS S3 Configuration for File Uploads (Required for file upload features)
# Get these from your AWS IAM console
AWS_REGION="us-east-1"
AWS_ACCESS_KEY_ID="your_aws_access_key_id"
AWS_SECRET_ACCESS_KEY="your_aws_secret_access_key"
S3_BUCKET_NAME="your_s3_bucket_name"
S3_BUCKET_URL="https://your_s3_bucket_name.s3.us-east-1.amazonaws.com"

# Optional: Custom S3 endpoint (for S3-compatible services like DigitalOcean Spaces)
# S3_ENDPOINT="https://your-custom-endpoint.com"

# Rate Limiting
RATE_LIMIT_WINDOW="900000"  # 15 minutes in milliseconds
RATE_LIMIT_MAX="100"        # Max requests per window

# Security
CORS_ORIGIN="http://localhost:3000"
SECURE_COOKIES="false"      # Set to true in production with HTTPS

# Feature Flags
ENABLE_REGISTRATION="true"
ENABLE_EMAIL_VERIFICATION="true"
ENABLE_PASSWORD_RESET="true"

# Admin Configuration
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"  # Change in production

# Deployment Configuration
DOKPLOY_APP_NAME="leaderbox"
DOKPLOY_DOMAIN="yourdomain.com"
