version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: leaderbox-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: leaderbox
      POSTGRES_USER: leaderbox
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD:-leaderbox123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./prisma/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - leaderbox-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U leaderbox -d leaderbox"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: leaderbox-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - leaderbox-network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # SvelteKit Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - BUILDKIT_INLINE_CACHE=1
    container_name: leaderbox-app
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      HOST: 0.0.0.0
      DATABASE_URL: postgresql://leaderbox:${DATABASE_PASSWORD:-leaderbox123}@postgres:5432/leaderbox
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis123}@redis:6379
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET:-your-nextauth-secret-change-in-production}
      NEXTAUTH_URL: ${NEXTAUTH_URL:-http://localhost:3000}
      EMAIL_SERVER_HOST: ${EMAIL_SERVER_HOST:-smtp.gmail.com}
      EMAIL_SERVER_PORT: ${EMAIL_SERVER_PORT:-587}
      EMAIL_SERVER_USER: ${EMAIL_SERVER_USER}
      EMAIL_SERVER_PASSWORD: ${EMAIL_SERVER_PASSWORD}
      EMAIL_FROM: ${EMAIL_FROM:-<EMAIL>}
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - leaderbox-network
    volumes:
      - uploads_data:/app/uploads
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (optional for production)
  nginx:
    image: nginx:alpine
    container_name: leaderbox-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - leaderbox-network
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local

networks:
  leaderbox-network:
    driver: bridge
